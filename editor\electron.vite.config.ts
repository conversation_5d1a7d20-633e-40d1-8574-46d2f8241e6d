import { defineConfig } from 'electron-vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

export default defineConfig({
  main: {
    build: {
      rollupOptions: {
        external: ['electron', 'ws', 'bufferutil', 'utf-8-validate']
      }
    },
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '../shared')
      }
    }
  },
  preload: {
    build: {
      rollupOptions: {
        external: ['electron']
      }
    },
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '../shared')
      }
    }
  },
  renderer: {
    root: 'src/renderer',
    build: {
      outDir: 'out/renderer',
      rollupOptions: {
        input: resolve(__dirname, 'src/renderer/index.html')
      }
    },
    plugins: [react()],
    resolve: {
      alias: {
        '@shared': resolve(__dirname, '../shared'),
        '@': resolve(__dirname, 'src/renderer'),
        '@/components': resolve(__dirname, 'src/renderer/components'),
        '@/lib': resolve(__dirname, 'src/renderer/lib')
      }
    }
  }
})
