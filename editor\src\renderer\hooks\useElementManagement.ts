import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Element, TextElement, ImageElement, VideoElement, AudioElement, LiveStreamElement, Scene, EditorState } from '@shared/types';

interface UseElementManagementProps {
    activeScene: Scene | null | undefined;
    selectedElement: Element | null;
    editorState: EditorState;
    saveToHistory: (state: EditorState) => void;
    updateScene: (scene: Scene) => void;
    setSelectedElement: (element: Element | null) => void;
    api: any;
}

export const useElementManagement = ({
    activeScene,
    selectedElement,
    editorState,
    saveToHistory,
    updateScene,
    setSelectedElement,
    api
}: UseElementManagementProps) => {

    // Add new elements
    const addTextElement = useCallback(() => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const newElement: TextElement = {
            id: uuidv4(),
            type: 'text',
            name: 'Text Element',
            visible: true,
            content: 'Sample Text',
            transform: {
                x: 100,
                y: 100,
                width: 200,
                height: 50
            },
            style: {
                fontSize: 24,
                fontFamily: 'Arial',
                color: '#ffffff',
                fontWeight: 'normal',
                textAlign: 'left'
            },
            zIndex: activeScene.elements.length
        };
        
        const updatedScene = {
            ...activeScene,
            elements: [...activeScene.elements, newElement]
        };
        
        updateScene(updatedScene);
    }, [activeScene, editorState, saveToHistory, updateScene]);

    const addImageElement = useCallback(async () => {
        if (!activeScene) return;
        
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'image/*';
        input.onchange = async (event) => {
            const file = (event.target as HTMLInputElement).files?.[0];
            if (file) {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const buffer = new Uint8Array(arrayBuffer);
                    
                    const uploadResult = await api.uploadMedia(buffer, file.name, file.type);
                    
                    if (uploadResult.success && uploadResult.url) {
                        saveToHistory(editorState);
                        
                        const newElement: ImageElement = {
                            id: uuidv4(),
                            type: 'image',
                            name: file.name.split('.')[0],
                            visible: true,
                            src: uploadResult.url,
                            alt: file.name,
                            transform: {
                                x: 100,
                                y: 100,
                                width: 300,
                                height: 200
                            },
                            style: {
                                borderRadius: 0,
                                opacity: 1
                            },
                            zIndex: activeScene.elements.length
                        };
                        
                        const updatedScene = {
                            ...activeScene,
                            elements: [...activeScene.elements, newElement]
                        };
                        
                        updateScene(updatedScene);
                    } else {
                        console.error('Failed to upload image:', uploadResult.error);
                        alert(`Failed to upload image: ${uploadResult.error}`);
                    }
                } catch (error) {
                    console.error('Error processing image:', error);
                    alert(`Error processing image: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        };
        input.click();
    }, [activeScene, editorState, saveToHistory, updateScene, api]);

    const addVideoElement = useCallback(async () => {
        if (!activeScene) return;
        
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'video/*';
        input.onchange = async (event) => {
            const file = (event.target as HTMLInputElement).files?.[0];
            if (file) {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const buffer = new Uint8Array(arrayBuffer);
                    
                    const uploadResult = await api.uploadMedia(buffer, file.name, file.type);
                    
                    if (uploadResult.success && uploadResult.url) {
                        saveToHistory(editorState);
                        
                        const newElement: VideoElement = {
                            id: uuidv4(),
                            type: 'video',
                            name: file.name.split('.')[0],
                            visible: true,
                            src: uploadResult.url,
                            transform: {
                                x: 100,
                                y: 100,
                                width: 400,
                                height: 225
                            },
                            style: {
                                borderRadius: 0,
                                opacity: 1
                            },
                            autoplay: false,
                            loop: false,
                            muted: true,
                            zIndex: activeScene.elements.length
                        };
                        
                        const updatedScene = {
                            ...activeScene,
                            elements: [...activeScene.elements, newElement]
                        };
                        
                        updateScene(updatedScene);
                    } else {
                        console.error('Failed to upload video:', uploadResult.error);
                        alert(`Failed to upload video: ${uploadResult.error}`);
                    }
                } catch (error) {
                    console.error('Error processing video:', error);
                    alert(`Error processing video: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        };
        input.click();
    }, [activeScene, editorState, saveToHistory, updateScene, api]);

    const addAudioElement = useCallback(async () => {
        if (!activeScene) return;
        
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = 'audio/*';
        input.onchange = async (event) => {
            const file = (event.target as HTMLInputElement).files?.[0];
            if (file) {
                try {
                    const arrayBuffer = await file.arrayBuffer();
                    const buffer = new Uint8Array(arrayBuffer);
                    
                    const uploadResult = await api.uploadMedia(buffer, file.name, file.type);
                    
                    if (uploadResult.success && uploadResult.url) {
                        const newElement: AudioElement = {
                            id: uuidv4(),
                            type: 'audio',
                            name: file.name.split('.')[0],
                            visible: true,
                            src: uploadResult.url,
                            transform: {
                                x: 100,
                                y: 100,
                                width: 200,
                                height: 100
                            },
                            style: {
                                opacity: 1
                            },
                            autoplay: false,
                            loop: false,
                            muted: false,
                            volume: 1,
                            zIndex: activeScene.elements.length
                        };
                        
                        const updatedScene = {
                            ...activeScene,
                            elements: [...activeScene.elements, newElement]
                        };
                        
                        saveToHistory(editorState);
                        updateScene(updatedScene);
                    }
                } catch (error) {
                    console.error('Error processing audio:', error);
                    alert(`Error processing audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
                }
            }
        };
        input.click();
    }, [activeScene, editorState, saveToHistory, updateScene, api]);

    const addLiveStreamElement = useCallback((streamData: {
        originalUrl: string;
        streamUrl: string;
        platform: string;
        title: string;
    }) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        // Create proxied URL to avoid CORS issues
        // Note: Using default port 3001, but media server may use different port if 3001 is busy
        const proxiedStreamUrl = streamData.streamUrl 
            ? `http://localhost:3001/stream/${encodeURIComponent(streamData.streamUrl)}`
            : '';
        
        const newElement: LiveStreamElement = {
            id: uuidv4(),
            type: 'livestream',
            name: streamData.title || 'Live Stream',
            visible: true,
            originalUrl: streamData.originalUrl,
            streamUrl: proxiedStreamUrl, // Use proxied URL
            platform: streamData.platform as 'youtube' | 'twitch' | 'kick' | 'other',
            transform: {
                x: 100,
                y: 100,
                width: 400,
                height: 225
            },
            style: {
                borderRadius: 8,
                opacity: 1
            },
            autoplay: true,
            muted: true, // Start muted to comply with browser autoplay policies
            volume: 0.8,
            lastUrlRefresh: new Date(),
            zIndex: activeScene.elements.length
        };
        
        const updatedScene = {
            ...activeScene,
            elements: [...activeScene.elements, newElement]
        };
        
        updateScene(updatedScene);
        setSelectedElement(newElement);
        
        return newElement;
    }, [activeScene, editorState, saveToHistory, updateScene, setSelectedElement]);

    // Update element properties
    const updateTextElement = useCallback((elementId: string, updates: Partial<TextElement>) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, ...updates } as TextElement
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, ...updates } as Element);
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    const updateImageElement = useCallback((elementId: string, updates: Partial<ImageElement>) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, ...updates } as ImageElement
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, ...updates } as Element);
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    const updateVideoElement = useCallback((elementId: string, updates: Partial<VideoElement>) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, ...updates } as VideoElement
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, ...updates } as Element);
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    const updateAudioElement = useCallback((elementId: string, updates: Partial<AudioElement>) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, ...updates } as AudioElement
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, ...updates } as Element);
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    const updateLiveStreamElement = useCallback((elementId: string, updates: Partial<LiveStreamElement>) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, ...updates } as LiveStreamElement
                : el
        );
        
        const updatedScene = { ...activeScene, elements: updatedElements };
        updateScene(updatedScene);
        
        // Update selected element if it's the one being updated
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, ...updates } as Element);
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    // Element operations
    const deleteElement = useCallback((elementId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const updatedScene = {
            ...activeScene,
            elements: activeScene.elements.filter(el => el.id !== elementId)
        };
        
        updateScene(updatedScene);
        if (selectedElement?.id === elementId) {
            setSelectedElement(null);
        }
    }, [activeScene, editorState, selectedElement, saveToHistory, updateScene, setSelectedElement]);

    const toggleElementVisibility = useCallback((elementId: string) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, visible: !(el.visible ?? true) }
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, visible: !(selectedElement.visible ?? true) });
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    const renameElement = useCallback((elementId: string, newName: string) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, name: newName }
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, name: newName });
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    const updateElementTransform = useCallback((elementId: string, updates: Partial<Element['transform']>) => {
        if (!activeScene) return;
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, transform: { ...el.transform, ...updates } }
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, transform: { ...selectedElement.transform, ...updates } });
        }
    }, [activeScene, selectedElement, updateScene, setSelectedElement]);

    // Copy and paste operations
    const copyElement = useCallback((element: Element | null) => {
        if (element) {
            return JSON.parse(JSON.stringify(element));
        }
        return null;
    }, []);

    const pasteElement = useCallback((copiedElement: Element) => {
        if (!copiedElement || !activeScene) return;
        
        const newElement = {
            ...JSON.parse(JSON.stringify(copiedElement)),
            id: uuidv4(),
            transform: {
                ...copiedElement.transform,
                x: copiedElement.transform.x + 20,
                y: copiedElement.transform.y + 20
            }
        };
        
        const updatedScene = {
            ...activeScene,
            elements: [...activeScene.elements, newElement]
        };
        
        saveToHistory(editorState);
        updateScene(updatedScene);
        setSelectedElement(newElement);
        
        return newElement;
    }, [activeScene, editorState, saveToHistory, updateScene, setSelectedElement]);

    const duplicateElement = useCallback((element: Element | null) => {
        if (!element || !activeScene) return;
        
        const newElement = {
            ...JSON.parse(JSON.stringify(element)),
            id: uuidv4(),
            transform: {
                ...element.transform,
                x: element.transform.x + 20,
                y: element.transform.y + 20
            }
        };
        
        const updatedScene = {
            ...activeScene,
            elements: [...activeScene.elements, newElement]
        };
        
        saveToHistory(editorState);
        updateScene(updatedScene);
        setSelectedElement(newElement);
        
        return newElement;
    }, [activeScene, editorState, saveToHistory, updateScene, setSelectedElement]);

    return {
        // Add operations
        addTextElement,
        addImageElement,
        addVideoElement,
        addAudioElement,
        addLiveStreamElement,
        
        // Update operations
        updateTextElement,
        updateImageElement,
        updateVideoElement,
        updateAudioElement,
        updateLiveStreamElement,
        updateElementTransform,
        
        // Element operations
        deleteElement,
        toggleElementVisibility,
        renameElement,
        
        // Copy/paste operations
        copyElement,
        pasteElement,
        duplicateElement
    };
};
