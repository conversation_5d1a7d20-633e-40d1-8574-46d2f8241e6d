import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Download, Loader2, CheckCircle, AlertCircle, Clock, Settings, ChevronDown, ChevronUp, Video, Volume2, Radio } from 'lucide-react';

interface MediaDownloaderProps {
  isOpen: boolean;
  onClose: () => void;
  downloadType?: 'video' | 'audio';
  onStreamExtracted?: (streamData: {
    originalUrl: string;
    streamUrl: string;
    platform: string;
    title: string;
  }) => void;
}

export const MediaDownloader: React.FC<MediaDownloaderProps> = ({ isOpen, onClose, downloadType: propDownloadType = 'video', onStreamExtracted }) => {
  const [url, setUrl] = useState('');
  const [downloadType, setDownloadType] = useState<'video' | 'audio'>(propDownloadType);
  const [isDownloading, setIsDownloading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | 'warning' | ''>('');
  const [ytdlpReady, setYtdlpReady] = useState(false);
  const [checkingStatus, setCheckingStatus] = useState(true);
  const [useCookies, setUseCookies] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      checkYtDlpStatus();
      setDownloadType(propDownloadType); // Update download type when modal opens
    }
  }, [isOpen, propDownloadType]);

  const checkYtDlpStatus = async () => {
    try {
      setCheckingStatus(true);
      const status = await window.electronAPI.getYtDlpStatus();
      setYtdlpReady(status.ready);
      
      if (!status.ready) {
        setMessage('Setting up media downloader... This may take a moment on first use.');
        setMessageType('warning');
      } else {
        setMessage('');
        setMessageType('');
      }
    } catch (error) {
      setYtdlpReady(false);
      setMessage('Failed to check downloader status');
      setMessageType('error');
    } finally {
      setCheckingStatus(false);
    }
  };

  const handleDownload = async () => {
    if (!url.trim()) {
      setMessage('Please enter a valid URL');
      setMessageType('error');
      return;
    }

    setIsDownloading(true);
    setMessage('');
    setMessageType('');

    try {
      // Regular download for video/audio
      const options: any = { useCookies };
      
      // Configure options based on download type
      if (downloadType === 'audio') {
        options.format = 'bestaudio/best';
        options.extractAudio = true;
        options.audioFormat = 'mp3';
        options.audioOnly = true;
        options.postProcessors = [{
          'key': 'FFmpegExtractAudio',
          'preferredcodec': 'mp3',
          'preferredquality': '192',
        }];
      }
      
      const result = await window.electronAPI.downloadVideo(url, options);
      
      if (result.success) {
        const mediaType = downloadType === 'audio' ? 'Audio' : 'Video';
        setMessage(result.message || `${mediaType} downloaded successfully`);
        setMessageType('success');
        setUrl('');
      } else {
        setMessage(result.error || 'Download failed');
        setMessageType('error');
      }
    } catch (error) {
      setMessage('An unexpected error occurred');
      setMessageType('error');
    } finally {
      setIsDownloading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !isDownloading) {
      handleDownload();
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-[99999]" style={{ zIndex: 99999 }}>
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw] max-h-[80vh] overflow-y-auto shadow-2xl border border-gray-200 dark:border-gray-600">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">
            Download {downloadType === 'audio' ? 'Audio' : 'Video'}
          </h2>
          <Button variant="outline" size="sm" onClick={onClose}>
            ×
          </Button>
        </div>
        
        <div className="space-y-4">
          {/* Download Type Selection */}
          <div className="relative">
            <label className="block text-sm font-medium mb-2">Download Type</label>
            <button
              type="button"
              onClick={() => setDropdownOpen(!dropdownOpen)}
              className="w-full flex items-center justify-between px-3 py-2 text-left bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
            >
              <div className="flex items-center gap-2">
                {downloadType === 'video' ? (
                  <>
                    <Video className="w-4 h-4" />
                    Video (MP4)
                  </>
                ) : downloadType === 'audio' ? (
                  <>
                    <Volume2 className="w-4 h-4" />
                    Audio Only (MP3)
                  </>
                ) : (
                  <>
                    <Radio className="w-4 h-4" />
                    Live Stream
                  </>
                )}
              </div>
              <ChevronDown className={`w-4 h-4 transition-transform ${dropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            
            {dropdownOpen && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg">
                <button
                  type="button"
                  onClick={() => {
                    setDownloadType('video');
                    setDropdownOpen(false);
                  }}
                  className={`w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 ${downloadType === 'video' ? 'bg-primary/10 text-primary' : ''}`}
                >
                  <Video className="w-4 h-4" />
                  Video (MP4)
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setDownloadType('audio');
                    setDropdownOpen(false);
                  }}
                  className={`w-full flex items-center gap-2 px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 ${downloadType === 'audio' ? 'bg-primary/10 text-primary' : ''}`}
                >
                  <Volume2 className="w-4 h-4" />
                  Audio Only (MP3)
                </button>
              </div>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              {downloadType === 'audio' ? 'Audio URL' : 'Video URL'} 
              (YouTube, Vimeo, etc.)
            </label>
            <Input
              type="url"
              placeholder="https://www.youtube.com/watch?v=..."
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={isDownloading}
              className="w-full"
            />
          </div>
          
          {/* Advanced Options */}
          <div className="border-t pt-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="w-full justify-between text-sm"
            >
              <span className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Advanced Options
              </span>
              {showAdvanced ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
            
            {showAdvanced && (
              <div className="mt-3 space-y-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                <label className="flex items-center gap-2 text-sm">
                  <input
                    type="checkbox"
                    checked={useCookies}
                    onChange={(e) => setUseCookies(e.target.checked)}
                    className="rounded"
                  />
                  Use browser cookies
                  <span className="text-xs text-gray-500">(helps with age-restricted or private content)</span>
                </label>
              </div>
            )}
          </div>
          
          {(message || checkingStatus) && (
            <div className={`flex items-center gap-2 p-3 rounded-md ${
              messageType === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
              messageType === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
              messageType === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' : ''
            }`}>
              {messageType === 'success' && <CheckCircle className="w-4 h-4" />}
              {messageType === 'error' && <AlertCircle className="w-4 h-4" />}
              {messageType === 'warning' && <Clock className="w-4 h-4" />}
              {checkingStatus && <Loader2 className="w-4 h-4 animate-spin" />}
              <span className="text-sm">
                {checkingStatus ? 'Checking downloader status...' : message}
              </span>
            </div>
          )}
          
          <div className="flex gap-3">
            <Button
              onClick={handleDownload}
              disabled={isDownloading || !url.trim() || checkingStatus}
              className="flex-1"
            >
              {isDownloading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  {downloadType === 'audio' ? 'Download Audio' : 'Download Video'}
                </>
              )}
            </Button>
            <Button variant="outline" onClick={onClose} disabled={isDownloading}>
              Cancel
            </Button>
          </div>
          
          <div className="text-xs text-gray-500 dark:text-gray-400">
            <p>
              {downloadType === 'audio' 
                ? 'Audio will be downloaded as MP3 to your Downloads folder.' 
                : 'Videos will be downloaded to your Downloads folder.'}
            </p>
            <p>
              Supports YouTube, Vimeo, SoundCloud, and other yt-dlp compatible sites.
            </p>
            {useCookies && (
              <p className="mt-1 text-yellow-600 dark:text-yellow-400">
                ⓘ Using browser cookies may help with age-restricted or private content
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  return createPortal(modalContent, document.body);
};