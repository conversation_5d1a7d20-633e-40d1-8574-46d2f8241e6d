import React, { useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { 
    Maximize, 
    ZoomIn, 
    ZoomOut,
    Type,
    Image,
    Video,
    Volume2,
    Radio,
    Upload,
    Download,
    ChevronDown,
    Plus,
    Camera,
    Settings
} from 'lucide-react';

interface HoverZonesProps {
    onCenter: () => void;
    zoomLevel: number | 'fit';
    onZoomChange: (zoom: number | 'fit') => void;
    addTextElement: () => void;
    addImageElement: () => void;
    addVideoElement: () => void;
    addAudioElement: () => void;
    addLiveStreamElement: () => void;
    onOpenDownloader?: () => void;
    onZoomToFit?: () => void;
    onPublish?: () => void;
    canPublish?: boolean;
    isExiting?: boolean;
    previewMode?: boolean;
    onDropdownOpenChange?: (isOpen: boolean) => void;
    selectedElement?: any;
    showPropertiesPanel?: boolean;
    onTogglePropertiesPanel?: () => void;
}

export const HoverZones: React.FC<HoverZonesProps> = ({
    onCenter,
    zoomLevel,
    onZoomChange,
    addTextElement,
    addImageElement,
    addVideoElement,
    addAudioElement,
    addLiveStreamElement,
    onOpenDownloader,
    onZoomToFit,
    onPublish,
    canPublish,
    isExiting = false,
    previewMode = false,
    onDropdownOpenChange,
    selectedElement,
    showPropertiesPanel = false,
    onTogglePropertiesPanel
}) => {
    const [isVisible, setIsVisible] = useState(false);
    const [openDropdowns, setOpenDropdowns] = useState<Set<string>>(new Set());
    
    const handleDropdownChange = (dropdownId: string, isOpen: boolean) => {
        setOpenDropdowns(prev => {
            const newSet = new Set(prev);
            if (isOpen) {
                newSet.add(dropdownId);
            } else {
                newSet.delete(dropdownId);
            }
            return newSet;
        });
    };
    
    // Notify parent when any dropdown state changes
    useEffect(() => {
        onDropdownOpenChange?.(openDropdowns.size > 0);
    }, [openDropdowns, onDropdownOpenChange]);

    // Trigger slide-in animation on mount
    useEffect(() => {
        if (!isExiting) {
            const timer = setTimeout(() => setIsVisible(true), 10);
            return () => clearTimeout(timer);
        }
    }, [isExiting]);

    // Trigger slide-out animation when exiting
    useEffect(() => {
        if (isExiting) {
            setIsVisible(false);
        }
    }, [isExiting]);
    const handleZoomIn = () => {
        if (zoomLevel === 'fit') {
            onZoomChange(100);
        } else if (typeof zoomLevel === 'number' && zoomLevel < 200) {
            onZoomChange(Math.min(200, zoomLevel + 25));
        }
    };

    const handleZoomOut = () => {
        if (zoomLevel === 'fit') {
            onZoomChange(75);
        } else if (typeof zoomLevel === 'number' && zoomLevel > 25) {
            onZoomChange(Math.max(25, zoomLevel - 25));
        }
    };

    return (
        <>

            {/* Bottom Edge - Unified Toolbar */}
            <Card className="fixed bottom-2 left-1/2 bg-background border-border shadow-xl z-30
                            transition-all duration-400 ease-out"
                  style={{
                      transform: `translateX(-50%) translateY(${isVisible ? '0' : '100%'})`,
                      opacity: isVisible ? 1 : 0,
                      backgroundColor: 'rgb(17, 24, 39)'
                  }}>
                <div className="flex items-center gap-2 p-3">
                    {/* Add Element Dropdown */}
                    {!previewMode && (
                        <DropdownMenu onOpenChange={(open) => handleDropdownChange('add', open)}>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm" className="h-8 px-3 gap-1">
                                    <Plus className="h-4 w-4" />
                                    <span className="text-xs font-medium">Add</span>
                                    <ChevronDown className="h-3 w-3" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="center" side="top" className="w-48">
                                <DropdownMenuItem onClick={addTextElement}>
                                    <Type className="h-4 w-4 mr-2" />
                                    Text
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={addImageElement}>
                                    <Image className="h-4 w-4 mr-2" />
                                    Image
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={addVideoElement}>
                                    <Video className="h-4 w-4 mr-2" />
                                    Upload Video
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={addAudioElement}>
                                    <Volume2 className="h-4 w-4 mr-2" />
                                    Upload Audio
                                </DropdownMenuItem>
                                {onOpenDownloader && (
                                    <DropdownMenuItem onClick={onOpenDownloader}>
                                        <Download className="h-4 w-4 mr-2" />
                                        Download Media
                                    </DropdownMenuItem>
                                )}
                                <DropdownMenuItem onClick={addLiveStreamElement}>
                                    <Radio className="h-4 w-4 mr-2" />
                                    Live Stream
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    )}

                    {/* Zoom Level Display */}
                    <div className="text-xs font-medium px-2 py-1 bg-muted rounded min-w-12 text-center">
                        {typeof zoomLevel === 'string' ? 'Fit' : `${zoomLevel}%`}
                    </div>
                    
                    {/* Properties Panel Toggle */}
                    {!previewMode && selectedElement && onTogglePropertiesPanel && (
                        <Button
                            variant={showPropertiesPanel ? "default" : "outline"}
                            size="sm"
                            onClick={onTogglePropertiesPanel}
                            className={`h-8 px-3 gap-1 ${
                                showPropertiesPanel 
                                    ? "bg-primary text-primary-foreground" 
                                    : ""
                            }`}
                            title="Toggle Properties Panel"
                        >
                            <Settings className="h-4 w-4" />
                            <span className="text-xs font-medium">Properties</span>
                        </Button>
                    )}
                    
                    {/* View Controls Dropdown */}
                    <DropdownMenu onOpenChange={(open) => handleDropdownChange('view', open)}>
                        <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm" className="h-8 px-3 gap-1">
                                <Camera className="h-4 w-4" />
                                <span className="text-xs font-medium">View</span>
                                <ChevronDown className="h-3 w-3" />
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="center" side="top" className="w-36">
                            <DropdownMenuItem onClick={handleZoomIn}>
                                <ZoomIn className="h-4 w-4 mr-2" />
                                Zoom In
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={handleZoomOut}>
                                <ZoomOut className="h-4 w-4 mr-2" />
                                Zoom Out
                            </DropdownMenuItem>
                            {onZoomToFit && (
                                <DropdownMenuItem onClick={onZoomToFit}>
                                    <Maximize className="h-4 w-4 mr-2" />
                                    Fit to Window
                                </DropdownMenuItem>
                            )}
                            <DropdownMenuItem onClick={onCenter}>
                                <Maximize className="h-4 w-4 mr-2" />
                                Center Canvas
                            </DropdownMenuItem>
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            </Card>
        </>
    );
};