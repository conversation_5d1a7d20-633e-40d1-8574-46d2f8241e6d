import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { X, GripVertical, Trash2, Edit3, ArrowUp, ArrowDown } from 'lucide-react';
import { ElementProperties } from '../editor/ElementProperties';
import type { Element } from '@shared/types';

interface FloatingPropertiesProps {
    element: Element;
    updateTextElement: (elementId: string, updates: any) => void;
    updateImageElement: (elementId: string, updates: any) => void;
    updateVideoElement: (elementId: string, updates: any) => void;
    updateAudioElement: (elementId: string, updates: any) => void;
    updateLiveStreamElement: (elementId: string, updates: any) => void;
    updateElementTransform: (elementId: string, transform: any) => void;
    deleteElement: (elementId: string) => void;
    renameElement: (elementId: string, name: string) => void;
    bringToFront: (elementId: string) => void;
    sendToBack: (elementId: string) => void;
    bringForward: (elementId: string) => void;
    sendBackward: (elementId: string) => void;
}

export const FloatingProperties: React.FC<FloatingPropertiesProps> = ({
    element,
    updateTextElement,
    updateImageElement,
    updateVideoElement,
    updateAudioElement,
    updateLiveStreamElement,
    updateElementTransform,
    deleteElement,
    renameElement,
    bringToFront,
    sendToBack,
    bringForward,
    sendBackward
}) => {
    const [position, setPosition] = useState({ x: 20, y: 100 });
    const [isDragging, setIsDragging] = useState(false);
    const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
    const [isCollapsed, setIsCollapsed] = useState(false);

    const handleMouseDown = (e: React.MouseEvent) => {
        setIsDragging(true);
        setDragOffset({
            x: e.clientX - position.x,
            y: e.clientY - position.y
        });
    };

    const handleMouseMove = (e: MouseEvent) => {
        if (isDragging) {
            setPosition({
                x: e.clientX - dragOffset.x,
                y: e.clientY - dragOffset.y
            });
        }
    };

    const handleMouseUp = () => {
        setIsDragging(false);
    };

    React.useEffect(() => {
        if (isDragging) {
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
            return () => {
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
            };
        }
    }, [isDragging]);

    return (
        <Card
            className="fixed bg-background border-border shadow-2xl max-w-80 z-40"
            style={{
                left: position.x,
                top: position.y,
                transform: isDragging ? 'scale(1.02)' : 'scale(1)',
                transition: isDragging ? 'none' : 'transform 0.2s ease',
                backgroundColor: 'rgb(17, 24, 39)'
            }}
        >
            <CardHeader className="pb-2 px-3 py-2">
                <div className="flex items-center justify-between">
                    <div
                        className="flex items-center gap-2 flex-1 cursor-move"
                        onMouseDown={handleMouseDown}
                    >
                        <GripVertical className="h-4 w-4 text-muted-foreground" />
                        <CardTitle className="text-sm font-semibold truncate">
                            {element.name}
                        </CardTitle>
                    </div>
                    
                    <div className="flex items-center gap-1">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setIsCollapsed(!isCollapsed)}
                            className="h-6 w-6 p-0"
                        >
                            {isCollapsed ? <ArrowDown className="h-3 w-3" /> : <ArrowUp className="h-3 w-3" />}
                        </Button>
                    </div>
                </div>
            </CardHeader>

            {!isCollapsed && (
                <CardContent className="p-3 pt-0 max-h-96 overflow-y-auto">
                    {/* Quick Actions */}
                    <div className="flex items-center gap-1 mb-3 pb-2 border-b border-border/30">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => bringForward(element.id)}
                            className="h-7 px-2 text-xs"
                            title="Bring Forward"
                        >
                            ↑
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => sendBackward(element.id)}
                            className="h-7 px-2 text-xs"
                            title="Send Backward"
                        >
                            ↓
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => bringToFront(element.id)}
                            className="h-7 px-2 text-xs"
                            title="Bring to Front"
                        >
                            ⇈
                        </Button>
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => sendToBack(element.id)}
                            className="h-7 px-2 text-xs"
                            title="Send to Back"
                        >
                            ⇊
                        </Button>
                        
                        <div className="ml-auto">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => deleteElement(element.id)}
                                className="h-7 px-2 text-xs text-destructive hover:bg-destructive/10"
                            >
                                <Trash2 className="h-3 w-3" />
                            </Button>
                        </div>
                    </div>

                    {/* Element Properties */}
                    <ElementProperties
                        selectedElement={element}
                        updateTextElement={updateTextElement}
                        updateImageElement={updateImageElement}
                        updateVideoElement={updateVideoElement}
                        updateAudioElement={updateAudioElement}
                        updateLiveStreamElement={updateLiveStreamElement}
                    />
                </CardContent>
            )}
        </Card>
    );
};