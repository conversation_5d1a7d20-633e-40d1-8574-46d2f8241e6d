name = "obs-browser-source"
main = "src/index.ts"
compatibility_date = "2023-10-30"

[env.production]
name = "obs-browser-source"

[durable_objects]
bindings = [
  { name = "BROWSER_SOURCE_STATE", class_name = "BrowserSourceState" }
]

[[r2_buckets]]
binding = "MEDIA_BUCKET"
bucket_name = "obs-editor-media"
preview_bucket_name = "obs-editor-media-preview"

[[migrations]]
tag = "v1"
new_classes = ["BrowserSourceState"]

[observability.logs]
enabled = true
