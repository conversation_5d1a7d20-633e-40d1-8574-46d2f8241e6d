import React, { useState, useEffect, useRef } from 'react';
import { Rnd } from 'react-rnd';
import { Scene, Element, TextElement, ImageElement, VideoElement, AudioElement, LiveStreamElement } from '@shared/types';
import Hls from 'hls.js';

interface CanvasProps {
    activeScene: Scene | null | undefined;
    selectedElement: Element | null;
    canvasSize: { width: number; height: number };
    zoomLevel: 'fit' | 25 | 50 | 75 | 100 | 150 | 200;
    calculateScale: () => number;
    previewMode: boolean;
    isDragging: boolean;
    isPanning: boolean;
    setSelectedElement: (element: Element | null) => void;
    setIsDragging: (dragging: boolean) => void;
    updateElementTransform: (id: string, updates: any) => void;
    handleCanvasPanStart: (e: React.MouseEvent) => void;
    handleCanvasPanMove: (e: React.MouseEvent) => void;
    handleCanvasPanEnd: () => void;
    containerStyle?: React.CSSProperties; // Override container styles for minimal layout
}

export const Canvas: React.FC<CanvasProps> = ({
    activeScene,
    selectedElement,
    canvasSize,
    zoomLevel,
    calculateScale,
    previewMode,
    isDragging,
    isPanning,
    setSelectedElement,
    setIsDragging,
    updateElementTransform,
    handleCanvasPanStart,
    handleCanvasPanMove,
    handleCanvasPanEnd,
    containerStyle
}) => {
    const handleCanvasClick = (e: React.MouseEvent) => {
        // Only clear selection if clicking directly on the canvas (not on an element)
        if (e.target === e.currentTarget && !previewMode) {
            setSelectedElement(null);
        }
    };
    // Keyboard modifier state for professional resize behavior
    const [isShiftPressed, setIsShiftPressed] = useState(false);
    
    // Handle keyboard events for modifiers
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.key === 'Shift') {
                setIsShiftPressed(true);
            }
        };
        
        const handleKeyUp = (e: KeyboardEvent) => {
            if (e.key === 'Shift') {
                setIsShiftPressed(false);
            }
        };
        
        window.addEventListener('keydown', handleKeyDown);
        window.addEventListener('keyup', handleKeyUp);
        
        return () => {
            window.removeEventListener('keydown', handleKeyDown);
            window.removeEventListener('keyup', handleKeyUp);
        };
    }, []);

    const renderElement = (element: Element) => {
        if (element.type === 'text') {
            const textElement = element as TextElement;
            return (
                <div 
                    className="w-full h-full flex items-center"
                    style={{
                        fontSize: textElement.style.fontSize,
                        fontFamily: textElement.style.fontFamily,
                        color: textElement.style.color,
                        fontWeight: textElement.style.fontWeight,
                        textAlign: textElement.style.textAlign,
                        overflow: 'hidden',
                        userSelect: 'none'
                    }}
                >
                    {textElement.content}
                </div>
            );
        }
        
        if (element.type === 'image') {
            const imageElement = element as ImageElement;
            return (
                <img
                    src={imageElement.src}
                    alt={imageElement.alt}
                    className="w-full h-full object-fill"
                    style={{
                        borderRadius: imageElement.style.borderRadius,
                        opacity: imageElement.style.opacity,
                        filter: imageElement.style.filter,
                        userSelect: 'none',
                        pointerEvents: 'none'
                    }}
                    draggable={false}
                />
            );
        }
        
        if (element.type === 'video') {
            const videoElement = element as VideoElement;
            return (
                <video
                    src={videoElement.src}
                    className="w-full h-full object-fill"
                    style={{
                        borderRadius: videoElement.style.borderRadius,
                        opacity: videoElement.style.opacity,
                        filter: videoElement.style.filter,
                        userSelect: 'none',
                        pointerEvents: 'none'
                    }}
                    autoPlay={videoElement.autoplay}
                    loop={videoElement.loop}
                    muted={videoElement.muted}
                    controls={false}
                />
            );
        }
        
        if (element.type === 'audio') {
            const audioElement = element as AudioElement;
            return (
                <div 
                    className="w-full h-full bg-gradient-to-br from-purple-500/20 to-blue-500/20 border-2 border-dashed border-purple-300 flex items-center justify-center rounded-lg"
                    style={{
                        opacity: audioElement.style.opacity,
                        userSelect: 'none',
                        pointerEvents: 'none'
                    }}
                >
                    <div className="text-center">
                        <div className="text-4xl mb-2">🎵</div>
                        <div className="text-sm font-medium text-purple-700">Audio</div>
                        <div className="text-xs text-purple-600">{audioElement.name}</div>
                    </div>
                    <audio
                        src={audioElement.src}
                        autoPlay={audioElement.autoplay}
                        loop={audioElement.loop}
                        muted={audioElement.muted}
                        controls={false}
                        style={{ display: 'none' }}
                        ref={(audio) => {
                            if (audio) {
                                audio.volume = audioElement.volume;
                            }
                        }}
                    />
                </div>
            );
        }
        
        if (element.type === 'livestream') {
            const liveStreamElement = element as LiveStreamElement;
            
            const HLSVideo = () => {
                const videoRef = useRef<HTMLVideoElement>(null);
                const hlsRef = useRef<Hls | null>(null);
                
                useEffect(() => {
                    if (videoRef.current) {
                        const video = videoRef.current;
                        const streamUrl = liveStreamElement.streamUrl;
                        
                        console.log('Setting up HLS for stream:', streamUrl);
                        
                        if (Hls.isSupported()) {
                            // Use HLS.js for browsers that support it
                            const hls = new Hls({
                                debug: false,
                                enableWorker: true,
                                lowLatencyMode: true,
                                backBufferLength: 90
                            });
                            
                            hlsRef.current = hls;
                            
                            hls.loadSource(streamUrl);
                            hls.attachMedia(video);
                            
                            hls.on(Hls.Events.MANIFEST_PARSED, () => {
                                console.log('HLS manifest parsed, starting playback');
                                if (liveStreamElement.autoplay) {
                                    video.play().catch(e => console.log('Autoplay prevented:', e));
                                }
                            });
                            
                            hls.on(Hls.Events.ERROR, (event, data) => {
                                console.error('HLS error:', data);
                                if (data.fatal) {
                                    switch (data.type) {
                                        case Hls.ErrorTypes.NETWORK_ERROR:
                                            console.log('Attempting to recover from network error');
                                            hls.startLoad();
                                            break;
                                        case Hls.ErrorTypes.MEDIA_ERROR:
                                            console.log('Attempting to recover from media error');
                                            hls.recoverMediaError();
                                            break;
                                        default:
                                            console.log('Cannot recover from error, destroying HLS');
                                            hls.destroy();
                                            break;
                                    }
                                }
                            });
                            
                        } else if (video.canPlayType('application/vnd.apple.mpegurl')) {
                            // Native HLS support (Safari)
                            console.log('Using native HLS support');
                            video.src = streamUrl;
                            if (liveStreamElement.autoplay) {
                                video.play().catch(e => console.log('Autoplay prevented:', e));
                            }
                        } else {
                            console.error('HLS not supported in this browser');
                        }
                        
                        return () => {
                            if (hlsRef.current) {
                                hlsRef.current.destroy();
                            }
                        };
                    }
                }, [liveStreamElement.streamUrl, liveStreamElement.autoplay]);
                
                return (
                    <video
                        ref={(video) => {
                            if (videoRef) {
                                (videoRef as React.MutableRefObject<HTMLVideoElement | null>).current = video;
                            }
                            if (video) {
                                video.volume = liveStreamElement.volume;
                            }
                        }}
                        className="w-full h-full object-fill"
                        style={{
                            borderRadius: liveStreamElement.style.borderRadius,
                            opacity: liveStreamElement.style.opacity,
                            filter: liveStreamElement.style.filter,
                            userSelect: 'none',
                            pointerEvents: 'none'
                        }}
                        muted={liveStreamElement.muted}
                        controls={false}
                        playsInline
                        onError={(e) => {
                            console.error('Video element error:', e);
                        }}
                    />
                );
            };
            
            return (
                <div className="w-full h-full relative">
                    <HLSVideo />
                </div>
            );
        }
        
        return null;
    };

    return (
        <div 
            className="bg-muted/10 relative" 
            style={containerStyle || { 
                overflow: 'auto',
                overflowX: 'auto',
                overflowY: 'auto',
                width: 'calc(100vw - 320px)',
                height: 'calc(100vh - 56px)'
            }}
            ref={(el) => {
                if (el) {
                    el.addEventListener('wheel', (e) => {
                        if (e.shiftKey) {
                            e.preventDefault();
                            el.scrollLeft += e.deltaY > 0 ? 50 : -50;
                        }
                    }, { passive: false });
                }
            }}
        >
            <div 
                style={{ 
                    width: (canvasSize.width + (zoomLevel === 'fit' ? 100 : 400)) * calculateScale(),
                    height: (canvasSize.height + (zoomLevel === 'fit' ? 100 : 400)) * calculateScale(),
                    position: 'relative'
                }}
            >
                <div
                    style={{
                        width: canvasSize.width + (zoomLevel === 'fit' ? 100 : 400),
                        height: canvasSize.height + (zoomLevel === 'fit' ? 100 : 400),
                        transform: `scale(${calculateScale()})`,
                        transformOrigin: 'top left',
                        position: 'relative'
                    }}
                >
                    {activeScene ? (
                        <div 
                            className="canvas absolute bg-black border-2 border-border rounded-lg shadow-xl"
                            style={{
                                width: canvasSize.width,
                                height: canvasSize.height,
                                left: zoomLevel === 'fit' ? 50 : 200,
                                top: zoomLevel === 'fit' ? 50 : 200,
                                backgroundImage: `
                                    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)
                                `,
                                backgroundSize: '50px 50px',
                                cursor: isPanning ? 'grabbing' : 'grab'
                            }}
                            onMouseDown={handleCanvasPanStart}
                            onMouseMove={handleCanvasPanMove}
                            onMouseUp={handleCanvasPanEnd}
                            onMouseLeave={handleCanvasPanEnd}
                            onClick={handleCanvasClick}
                        >
                            {activeScene.elements.map(element => (
                                <Rnd
                                    key={element.id}
                                    size={{
                                        width: element.transform.width * (element.transform.scaleX || 1),
                                        height: element.transform.height * (element.transform.scaleY || 1)
                                    }}
                                    position={{
                                        x: element.transform.x,
                                        y: element.transform.y
                                    }}
                                    onDragStart={(e, d) => {
                                        if (!previewMode) {
                                            setIsDragging(true);
                                            setSelectedElement(element);
                                        }
                                    }}
                                    onDrag={(e, d) => {
                                        // Let react-rnd handle the visual positioning during drag
                                    }}
                                    onDragStop={(e, d) => {
                                        if (!previewMode) {
                                            setIsDragging(false);
                                            updateElementTransform(element.id, {
                                                x: d.x,
                                                y: d.y
                                            });
                                        }
                                    }}
                                    onResizeStart={() => {
                                        if (!previewMode) {
                                            setSelectedElement(element);
                                        }
                                    }}
                                    onResize={(e, direction, ref, delta, position) => {
                                        // Let react-rnd handle the visual resizing during resize
                                    }}
                                    onResizeStop={(e, direction, ref, delta, position) => {
                                        if (!previewMode) {
                                            let newWidth = parseInt(ref.style.width);
                                            let newHeight = parseInt(ref.style.height);
                                            
                                            
                                            updateElementTransform(element.id, {
                                                width: newWidth / (element.transform.scaleX || 1),
                                                height: newHeight / (element.transform.scaleY || 1),
                                                x: position.x,
                                                y: position.y
                                            });
                                        }
                                    }}
                                    bounds="parent"
                                    enableResizing={!previewMode && element.id === selectedElement?.id ? {
                                        top: true,
                                        right: true,
                                        bottom: true,
                                        left: true,
                                        topRight: true,
                                        bottomRight: true,
                                        bottomLeft: true,
                                        topLeft: true,
                                    } : false}
                                    disableDragging={previewMode || (isDragging && element.id !== selectedElement?.id)}
                                    lockAspectRatio={!isShiftPressed}
                                    scale={calculateScale()}
                                    className={`${
                                        element.id === selectedElement?.id 
                                            ? 'ring-2 ring-primary ring-offset-1 shadow-lg shadow-primary/20' 
                                            : 'hover:ring-1 hover:ring-primary/30'
                                    } rounded transition-none`}
                                    onClick={() => !previewMode && setSelectedElement(element)}
                                    style={{
                                        zIndex: element.zIndex
                                    }}
                                    resizeHandleStyles={{
                                        top: {
                                            width: '8px',
                                            height: '8px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            top: '-4px',
                                            left: '50%',
                                            transform: 'translateX(-50%)',
                                            cursor: 'ns-resize'
                                        },
                                        right: {
                                            width: '8px',
                                            height: '8px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            right: '-4px',
                                            top: '50%',
                                            transform: 'translateY(-50%)',
                                            cursor: 'ew-resize'
                                        },
                                        bottom: {
                                            width: '8px',
                                            height: '8px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            bottom: '-4px',
                                            left: '50%',
                                            transform: 'translateX(-50%)',
                                            cursor: 'ns-resize'
                                        },
                                        left: {
                                            width: '8px',
                                            height: '8px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            left: '-4px',
                                            top: '50%',
                                            transform: 'translateY(-50%)',
                                            cursor: 'ew-resize'
                                        },
                                        topLeft: {
                                            width: '10px',
                                            height: '10px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            top: '-5px',
                                            left: '-5px',
                                            cursor: 'nw-resize'
                                        },
                                        topRight: {
                                            width: '10px',
                                            height: '10px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            top: '-5px',
                                            right: '-5px',
                                            cursor: 'ne-resize'
                                        },
                                        bottomLeft: {
                                            width: '10px',
                                            height: '10px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            bottom: '-5px',
                                            left: '-5px',
                                            cursor: 'sw-resize'
                                        },
                                        bottomRight: {
                                            width: '10px',
                                            height: '10px',
                                            backgroundColor: 'hsl(var(--primary))',
                                            border: '2px solid white',
                                            borderRadius: '2px',
                                            bottom: '-5px',
                                            right: '-5px',
                                            cursor: 'se-resize'
                                        }
                                    }}
                                >
                                    {renderElement(element)}
                                </Rnd>
                            ))}
                        </div>
                    ) : (
                        <div 
                            className="canvas absolute bg-black border-2 border-border rounded-lg shadow-xl flex items-center justify-center"
                            style={{
                                width: canvasSize.width,
                                height: canvasSize.height,
                                left: zoomLevel === 'fit' ? 50 : 200,
                                top: zoomLevel === 'fit' ? 50 : 200,
                                backgroundImage: `
                                    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0)
                                `,
                                backgroundSize: '50px 50px',
                                cursor: isPanning ? 'grabbing' : 'grab'
                            }}
                            onMouseDown={handleCanvasPanStart}
                            onMouseMove={handleCanvasPanMove}
                            onMouseUp={handleCanvasPanEnd}
                            onMouseLeave={handleCanvasPanEnd}
                        >
                            <div className="text-muted-foreground text-lg">
                                Create a scene to get started
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};