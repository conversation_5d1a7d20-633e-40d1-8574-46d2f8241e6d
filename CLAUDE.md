# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OBS Browser Source Editor is a custom browser source system for OBS with live editing capabilities. It consists of three main components:

- **editor/**: Electron desktop application for designing layouts with drag-and-drop functionality
- **browser-source/**: Cloudflare Worker hosting the browser source page that streams to OBS
- **shared/**: Shared TypeScript types and utilities used across both applications

The editor communicates with the browser source via WebSockets for real-time synchronization.

## Development Commands

### Editor (Electron App)
```bash
cd editor
npm install
npm run dev      # Start development server
npm run build    # Build for production
npm run preview  # Preview built app
npm run package  # Package for distribution
```

### Browser Source (Cloudflare Worker)
```bash
cd browser-source
npm install
npm run dev      # Start local development
npm run deploy   # Deploy to Cloudflare Workers
npm run build    # Build the worker bundle
```

## Architecture & Key Concepts

### Modular Hook System
The editor uses a sophisticated modular architecture with custom React hooks for maintainability:

- **`useEditorState`** (editor/src/renderer/hooks/useEditorState.ts): Core state management with undo/redo, debounced API updates
- **`useElementManagement`** (editor/src/renderer/hooks/useElementManagement.ts): CRUD operations for all element types (text, image, video, audio, livestream)
- **`useSceneManagement`**: Scene operations (create, duplicate, delete, publish)
- **`useLayering`**: Element z-index and layering operations
- **`useGrouping`**: Element grouping functionality
- **`useCanvasInteraction`**: Canvas panning and drag interactions
- **`useElementSelection`**: Element selection state and clipboard operations
- **`useZoom`**: Zoom and scaling functionality
- **`useKeyboardShortcuts`**: Keyboard shortcut handling

### Element Types & Shared Types
All element types are defined in `shared/types.ts`:
- **TextElement**: Customizable text overlays with styling
- **ImageElement**: Image display with transformations
- **VideoElement**: Video playback with controls
- **AudioElement**: Audio playback (invisible UI element)
- **LiveStreamElement**: Live stream integration via yt-dlp with HLS support

Each element has:
- `transform`: Position (x,y), size (width,height), rotation, scale, aspectRatioLocked
- `zIndex`: Layer ordering
- `visible`: Show/hide toggle
- `name`: User-defined label

### Scene Management
- **Draft vs Published**: Scenes exist as drafts until explicitly published
- **Live Publishing**: Only one scene can be live at a time - publishing a scene makes it live and marks all others as drafts
- **WebSocket Sync**: Published scenes are broadcast to browser source via WebSocket

### Media Handling
- **Local Media Server**: Electron app runs an Express server (port 3001 default) for serving uploaded media
- **Storage Service**: Manages file uploads and generates public URLs
- **yt-dlp Integration**: Downloads videos/audio and extracts live stream URLs with browser cookie support

### WebSocket Communication
- **Development**: `ws://127.0.0.1:8787/websocket`
- **Production**: Cloudflare Worker WebSocket endpoint
- **Message Types**: `update_scene`, `publish_scene`, `set_active_scene` (see shared/types.ts)

## Key Files to Understand

- `editor/src/main/index.ts`: Main Electron process with IPC handlers, yt-dlp integration, media server
- `editor/src/renderer/hooks/useEditorState.ts`: Core state management with history
- `editor/src/renderer/hooks/useElementManagement.ts`: Element CRUD operations
- `shared/types.ts`: All TypeScript interfaces and types
- `browser-source/src/index.ts`: Cloudflare Worker handling WebSocket and serving browser source

## Development Tips

### Element Creation
New elements are added through `useElementManagement` with automatic z-index assignment (`activeScene.elements.length`). Always save to history before mutations.

### Transform Updates
Use `updateElementTransform()` for position/size changes. The `aspectRatioLocked` property maintains proportions during resize.

### WebSocket Development
The editor auto-connects to WebSocket on startup. Check connection status with `get-websocket-status` IPC call.

### Media Server
The Express server auto-starts on app launch, trying port 3001 first, then incrementing if busy. Use `mediaServer.getPort()` for the actual port.

### yt-dlp Integration
yt-dlp binary is auto-downloaded to `userData/yt-dlp/` on first use. Supports browser cookies for authentication-required streams.

## File Structure Patterns

### Components
- `editor/src/renderer/components/ui/`: Reusable UI components (buttons, inputs, etc.)
- `editor/src/renderer/components/layout/`: Layout-specific components (sidebar, toolbar, etc.)
- `editor/src/renderer/components/editor/`: Editor-specific functionality (canvas, properties, etc.)

### Hooks
All custom hooks are in `editor/src/renderer/hooks/` and follow the pattern `use[Domain][Action]`.

### State Management
Use the existing hook system rather than external state management. The `useEditorState` hook provides undo/redo and persistence.