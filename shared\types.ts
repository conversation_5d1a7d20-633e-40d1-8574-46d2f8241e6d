// Shared types for the OBS Browser Source Editor

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface Transform extends Position, Size {
  rotation?: number;
  scaleX?: number;
  scaleY?: number;
  aspectRatioLocked?: boolean;
}

export interface TextElement {
  id: string;
  type: 'text';
  name?: string;
  visible?: boolean;
  content: string;
  transform: Transform;
  style: {
    fontSize: number;
    fontFamily: string;
    color: string;
    fontWeight: 'normal' | 'bold' | '100' | '200' | '300' | '400' | '500' | '600' | '700' | '800' | '900';
    textAlign: 'left' | 'center' | 'right';
    textShadow?: string;
    backgroundColor?: string;
    padding?: number;
    borderRadius?: number;
  };
  zIndex: number;
}

export interface ImageElement {
  id: string;
  type: 'image';
  name?: string;
  visible?: boolean;
  src: string;
  alt?: string;
  transform: Transform;
  style: {
    borderRadius?: number;
    opacity?: number;
    filter?: string;
  };
  zIndex: number;
}

export interface VideoElement {
  id: string;
  type: 'video';
  name?: string;
  visible?: boolean;
  src: string;
  transform: Transform;
  style: {
    borderRadius?: number;
    opacity?: number;
    filter?: string;
  };
  autoplay: boolean;
  loop: boolean;
  muted: boolean;
  zIndex: number;
}

export interface AudioElement {
  id: string;
  type: 'audio';
  name?: string;
  visible?: boolean;
  src: string;
  transform: Transform;
  style: {
    opacity?: number;
  };
  autoplay: boolean;
  loop: boolean;
  muted: boolean;
  volume: number; // 0-1
  zIndex: number;
}

export interface LiveStreamElement {
  id: string;
  type: 'livestream';
  name?: string;
  visible?: boolean;
  originalUrl: string; // The kick.com/twitch.tv URL
  streamUrl: string; // The extracted direct stream URL
  platform: 'youtube' | 'twitch' | 'kick' | 'other';
  transform: Transform;
  style: {
    borderRadius?: number;
    opacity?: number;
    filter?: string;
  };
  autoplay: boolean;
  muted: boolean;
  volume: number; // 0-1
  lastUrlRefresh?: Date;
  zIndex: number;
}

// Group interface for organizing elements
export interface ElementGroup {
  id: string;
  name: string;
  elementIds: string[];
  visible: boolean;
  locked: boolean;
  collapsed?: boolean; // For UI purposes
}

export type Element = TextElement | ImageElement | VideoElement | AudioElement | LiveStreamElement;

export interface Scene {
  id: string;
  name: string;
  elements: Element[];
  groups: ElementGroup[];
  canvasSize: Size;
  backgroundColor?: string;
  isDraft?: boolean;
  lastPublished?: Date;
}

// WebSocket message types
export interface UpdateSceneMessage {
  type: 'update_scene';
  scene: Scene;
}

export interface UpdateElementMessage {
  type: 'update_element';
  sceneId: string;
  element: Element;
}

export interface DeleteElementMessage {
  type: 'delete_element';
  sceneId: string;
  elementId: string;
}

export interface SetActiveSceneMessage {
  type: 'set_active_scene';
  sceneId: string;
}

export interface PublishSceneMessage {
  type: 'publish_scene';
  scene: Scene;
}

export interface RefreshStreamUrlRequestMessage {
  type: 'refresh_stream_url_request';
  elementId: string;
  originalUrl: string;
}

export interface RefreshStreamUrlResponseMessage {
  type: 'refresh_stream_url_response';
  success: boolean;
  elementId: string;
  newStreamUrl?: string;
  error?: string;
}

export type WebSocketMessage = 
  | UpdateSceneMessage 
  | UpdateElementMessage 
  | DeleteElementMessage 
  | SetActiveSceneMessage
  | PublishSceneMessage
  | RefreshStreamUrlRequestMessage
  | RefreshStreamUrlResponseMessage;

export interface EditorState {
  scenes: Scene[];
  publishedScene?: Scene;  // Single published scene instead of array
  activeSceneId?: string;
  canvasSize: Size;
}
