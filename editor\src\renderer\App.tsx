import React, { useState } from 'react';
import { ElectronAPI } from '../preload';
import { MinimalLayout } from './components/layout/MinimalLayout';
import { Canvas } from './components/editor/Canvas';
import { useEditorState } from './hooks/useEditorState';
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';
import { useZoom } from './hooks/useZoom';
import { useElementManagement } from './hooks/useElementManagement';
import { useSceneManagement } from './hooks/useSceneManagement';
import { useLayering } from './hooks/useLayering';
import { useGrouping } from './hooks/useGrouping';
import { useCanvasInteraction } from './hooks/useCanvasInteraction';
import { useElementSelection } from './hooks/useElementSelection';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from './components/ui/dialog';
import { Button } from './components/ui/button';
import { Input } from './components/ui/input';
import './globals.css';

const api = (window as any).electronAPI as ElectronAPI;

const App = () => {
    const [showLiveStreamDialog, setShowLiveStreamDialog] = useState(false);
    const [liveStreamUrl, setLiveStreamUrl] = useState('');

    const {
        editorState,
        setEditorState,
        saveToHistory,
        updateScene,
        selectScene
    } = useEditorState();

    
    const { zoomLevel, setZoomLevel, calculateScale, centerCanvas } = useZoom(editorState.canvasSize);
    
    const {
        selectedElement,
        setSelectedElement,
        copiedElement,
        setCopiedElement,
        editingSceneId,
        setEditingSceneId,
        editingElementId,
        setEditingElementId,
        previewMode,
        setPreviewMode,
        copyElement: copySelectedElement,
        clearSelection
    } = useElementSelection();

    const activeScene = previewMode 
        ? (editorState.publishedScene || editorState.scenes.find(s => s.id === editorState.activeSceneId))
        : editorState.scenes.find(s => s.id === editorState.activeSceneId);

    // Canvas interaction
    const {
        isDragging,
        setIsDragging,
        isPanning,
        handleCanvasPanStart,
        handleCanvasPanMove,
        handleCanvasPanEnd
    } = useCanvasInteraction();

    // Element management
    const {
        addTextElement,
        addImageElement,
        addVideoElement,
        addAudioElement,
        addLiveStreamElement,
        updateTextElement,
        updateImageElement,
        updateVideoElement,
        updateAudioElement,
        updateLiveStreamElement,
        updateElementTransform,
        deleteElement,
        toggleElementVisibility,
        renameElement,
        copyElement,
        pasteElement,
        duplicateElement
    } = useElementManagement({
        activeScene,
        selectedElement,
        editorState,
        saveToHistory,
        updateScene,
        setSelectedElement,
        api
    });

    // Scene management
    const {
        createNewScene,
        deleteScene,
        duplicateScene,
        publishScene,
        renameScene,
        reorderScenes
    } = useSceneManagement({
        editorState,
        setEditorState,
        saveToHistory,
        updateScene,
        selectScene,
        api
    });

    // Layering
    const {
        bringToFront,
        sendToBack,
        bringForward,
        sendBackward
    } = useLayering({
        activeScene,
        selectedElement,
        editorState,
        saveToHistory,
        updateScene,
        setSelectedElement
    });

    // Grouping
    const {
        createGroup,
        ungroupElements,
        addToGroup,
        removeFromGroup,
        toggleGroupVisibility,
        toggleGroupLock,
        renameGroup
    } = useGrouping({
        activeScene,
        editorState,
        saveToHistory,
        updateScene
    });

    // Enhanced copy/paste operations
    const handleCopyElement = () => {
        copySelectedElement();
    };

    const handlePasteElement = () => {
        if (copiedElement) {
            pasteElement(copiedElement);
        }
    };

    const handleDuplicateElement = () => {
        duplicateElement(selectedElement);
    };

    const handleAddLiveStreamElement = () => {
        // Show dialog to get stream URL from user
        setLiveStreamUrl('');
        setShowLiveStreamDialog(true);
    };

    const handleLiveStreamUrlSubmit = async () => {
        if (!liveStreamUrl.trim()) {
            setShowLiveStreamDialog(false);
            return;
        }

        try {
            // Try to extract stream URL using yt-dlp for supported platforms
            const result = await api.extractStreamUrl(liveStreamUrl.trim(), false);
            
            if (result.success && result.streamUrl) {
                const streamData = {
                    originalUrl: liveStreamUrl.trim(),
                    streamUrl: result.streamUrl,
                    platform: result.platform || 'other',
                    title: result.title || 'Live Stream'
                };
                addLiveStreamElement(streamData);
            } else {
                // Fallback: use URL directly if extraction fails
                const streamData = {
                    originalUrl: liveStreamUrl.trim(),
                    streamUrl: liveStreamUrl.trim(),
                    platform: 'other',
                    title: 'Live Stream'
                };
                addLiveStreamElement(streamData);
            }
        } catch (error) {
            console.error('Error extracting stream URL:', error);
            // Fallback: use URL directly if extraction fails
            const streamData = {
                originalUrl: liveStreamUrl.trim(),
                streamUrl: liveStreamUrl.trim(),
                platform: 'other',
                title: 'Live Stream'
            };
            addLiveStreamElement(streamData);
        }

        setShowLiveStreamDialog(false);
        setLiveStreamUrl('');
    };

    const handleAddLiveStreamElementWithData = (streamData: {
        originalUrl: string;
        streamUrl: string;
        platform: string;
        title: string;
    }) => {
        // Create a live stream element with the provided data
        addLiveStreamElement(streamData);
    };

    const handlePublishScene = () => {
        if (activeScene) {
            publishScene(activeScene.id);
        }
    };

    const handleZoomChange = (zoom: number | 'fit') => {
        if (zoom === 'fit') {
            setZoomLevel('fit');
        } else {
            // Convert number to valid zoom level
            const validZoomLevels: (25 | 50 | 75 | 100 | 150 | 200)[] = [25, 50, 75, 100, 150, 200];
            const closestZoom = validZoomLevels.reduce((prev, curr) => 
                Math.abs(curr - zoom) < Math.abs(prev - zoom) ? curr : prev
            );
            setZoomLevel(closestZoom);
        }
    };

    useKeyboardShortcuts({
        copyElement: handleCopyElement,
        pasteElement: handlePasteElement,
        duplicateElement: handleDuplicateElement,
        selectedElement,
        activeScene,
        editorState,
        updateElementTransform,
        updateScene,
        setSelectedElement,
        saveToHistory,
        zoomLevel,
        setZoomLevel
    });

    return (
        <>
        <MinimalLayout
            editorState={editorState}
            activeScene={activeScene}
            selectedElement={selectedElement}
            previewMode={previewMode}
            setPreviewMode={setPreviewMode}
            selectScene={selectScene}
            createNewScene={createNewScene}
            deleteScene={deleteScene}
            duplicateScene={duplicateScene}
            renameScene={renameScene}
            reorderScenes={reorderScenes}
            setSelectedElement={setSelectedElement}
            clearSelection={clearSelection}
            deleteElement={deleteElement}
            toggleElementVisibility={toggleElementVisibility}
            renameElement={renameElement}
            addTextElement={addTextElement}
            addImageElement={addImageElement}
            addVideoElement={addVideoElement}
            addAudioElement={addAudioElement}
            addLiveStreamElement={handleAddLiveStreamElement}
            addLiveStreamElementWithData={handleAddLiveStreamElementWithData}
            updateTextElement={updateTextElement}
            updateImageElement={updateImageElement}
            updateVideoElement={updateVideoElement}
            updateAudioElement={updateAudioElement}
            updateLiveStreamElement={updateLiveStreamElement}
            updateElementTransform={updateElementTransform}
            bringToFront={bringToFront}
            sendToBack={sendToBack}
            bringForward={bringForward}
            sendBackward={sendBackward}
            createGroup={createGroup}
            ungroupElements={ungroupElements}
            addToGroup={addToGroup}
            removeFromGroup={removeFromGroup}
            toggleGroupVisibility={toggleGroupVisibility}
            toggleGroupLock={toggleGroupLock}
            renameGroup={renameGroup}
            canvasSize={editorState.canvasSize}
            zoomLevel={zoomLevel}
            calculateScale={calculateScale}
            isDragging={isDragging}
            isPanning={isPanning}
            setIsDragging={setIsDragging}
            handleCanvasPanStart={handleCanvasPanStart}
            handleCanvasPanMove={handleCanvasPanMove}
            handleCanvasPanEnd={handleCanvasPanEnd}
            onSave={() => api.saveProject()}
            onLoad={() => api.loadProject()}
            onCopy={handleCopyElement}
            onPaste={handlePasteElement}
            onDuplicate={handleDuplicateElement}
            onPublish={handlePublishScene}
            onCenter={centerCanvas}
            onZoomChange={handleZoomChange}
            copiedElement={copiedElement}
            onLayoutToggle={() => {}}
        />

        {/* Live Stream URL Dialog */}
        <Dialog open={showLiveStreamDialog} onOpenChange={setShowLiveStreamDialog}>
            <DialogContent className="sm:max-w-md" style={{ backgroundColor: 'rgb(17, 24, 39)' }}>
                <DialogHeader>
                    <DialogTitle>Add Live Stream</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                    <div className="space-y-2">
                        <label htmlFor="stream-url" className="text-sm font-medium">
                            Stream URL
                        </label>
                        <Input
                            id="stream-url"
                            type="url"
                            placeholder="Enter livestream URL (YouTube, Twitch, Kick, etc.)"
                            value={liveStreamUrl}
                            onChange={(e) => setLiveStreamUrl(e.target.value)}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    e.preventDefault();
                                    handleLiveStreamUrlSubmit();
                                }
                            }}
                            autoFocus
                        />
                        <p className="text-xs text-muted-foreground">
                            Supports direct stream URLs and major platforms (YouTube, Twitch, Kick)
                        </p>
                    </div>
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={() => setShowLiveStreamDialog(false)}>
                        Cancel
                    </Button>
                    <Button onClick={handleLiveStreamUrlSubmit} disabled={!liveStreamUrl.trim()}>
                        Add Stream
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
        </>
    );
};

export default App;