import { useState, useCallback, useEffect } from 'react';

export const useZoom = (canvasSize: { width: number; height: number }) => {
    const [zoomLevel, setZoomLevel] = useState<'fit' | 25 | 50 | 75 | 100 | 150 | 200>('fit');

    const calculateScale = useCallback(() => {
        if (zoomLevel === 'fit') {
            const availableWidth = window.innerWidth - 320 - 40;
            const availableHeight = window.innerHeight - 56 - 40;
            
            const scaleX = availableWidth / canvasSize.width;
            const scaleY = availableHeight / canvasSize.height;
            
            return Math.min(scaleX, scaleY, 1);
        }
        
        return zoomLevel / 100;
    }, [zoomLevel, canvasSize]);

    useEffect(() => {
        const handleResize = () => {
            if (zoomLevel === 'fit') {
                setZoomLevel('fit');
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [zoomLevel]);

    const centerCanvas = useCallback(() => {
        const canvasContainer = document.querySelector('[style*="overflow"]');
        if (canvasContainer) {
            canvasContainer.scrollTo({
                left: (canvasContainer.scrollWidth - canvasContainer.clientWidth) / 2,
                top: (canvasContainer.scrollHeight - canvasContainer.clientHeight) / 2,
                behavior: 'smooth'
            });
        }
    }, []);

    return {
        zoomLevel,
        setZoomLevel,
        calculateScale,
        centerCanvas
    };
};