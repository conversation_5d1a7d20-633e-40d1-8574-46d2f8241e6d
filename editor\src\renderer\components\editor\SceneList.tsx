import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Plus, Monitor, MoreVertical, Edit2, Copy as CopyIcon, Trash2 } from 'lucide-react';
import { EditorState } from '@shared/types';

interface SceneListProps {
    editorState: EditorState;
    previewMode: boolean;
    editingSceneId: string | null;
    setPreviewMode: (mode: boolean) => void;
    setEditingSceneId: (id: string | null) => void;
    selectScene: (id: string) => void;
    createNewScene: () => void;
    deleteScene: (id: string) => void;
    duplicateScene: (id: string) => void;
    renameScene: (id: string, name: string) => void;
}

export const SceneList: React.FC<SceneListProps> = ({
    editorState,
    previewMode,
    editingSceneId,
    setPreviewMode,
    setEditingSceneId,
    selectScene,
    createNewScene,
    deleteScene,
    duplicateScene,
    renameScene
}) => {
    const scenes = previewMode 
        ? (editorState.publishedScene ? [editorState.publishedScene] : editorState.scenes)
        : editorState.scenes;

    return (
        <div className="space-y-3 p-3">
                {previewMode && (
                    <div className={`${editorState.publishedScene ? 'bg-blue-50 border-blue-200 text-blue-700' : 'bg-orange-50 border-orange-200 text-orange-700'} border rounded-lg p-3 text-center`}>
                        <div className="flex items-center justify-center gap-2">
                            <Monitor className="w-4 h-4" />
                            <span className="text-sm font-medium">
                                {editorState.publishedScene ? 'Live Preview Mode' : 'Preview Mode (No Published Scene)'}
                            </span>
                        </div>
                        <p className="text-xs mt-1">
                            {editorState.publishedScene
                                ? 'Showing published version as seen by viewers' 
                                : 'No published scene found. Showing draft scenes for preview.'}
                        </p>
                    </div>
                )}
                <div className="space-y-2">
                    {scenes.map(scene => (
                        <div key={scene.id} className="flex items-center gap-2">
                            <Button
                                variant={scene.id === editorState.activeSceneId ? "default" : "ghost"}
                                className={`flex-1 justify-start h-auto p-3 transition-all ${
                                    scene.id === editorState.activeSceneId 
                                        ? "bg-primary text-primary-foreground border-2 border-primary shadow-md" 
                                        : "hover:bg-accent hover:text-accent-foreground border-2 border-transparent"
                                }`}
                                onClick={() => selectScene(scene.id)}
                                onDoubleClick={() => !previewMode && setEditingSceneId(scene.id)}
                            >
                                {editingSceneId === scene.id ? (
                                    <Input
                                        defaultValue={scene.name}
                                        className="text-sm border-none p-0 h-auto bg-transparent"
                                        onBlur={(e: React.FocusEvent<HTMLInputElement>) => {
                                            renameScene(scene.id, e.target.value);
                                            setEditingSceneId(null);
                                        }}
                                        onKeyDown={(e: React.KeyboardEvent<HTMLInputElement>) => {
                                            if (e.key === 'Enter') {
                                                renameScene(scene.id, e.currentTarget.value);
                                                setEditingSceneId(null);
                                            } else if (e.key === 'Escape') {
                                                setEditingSceneId(null);
                                            }
                                        }}
                                        autoFocus
                                        onClick={(e: React.MouseEvent) => e.stopPropagation()}
                                    />
                                ) : (
                                    <div className="flex items-center justify-between w-full">
                                        <span className="font-medium">{scene.name}</span>
                                        <div className="flex items-center gap-1">
                                            {!scene.isDraft && (
                                                <Badge variant="destructive" className="text-xs bg-red-500">
                                                    LIVE
                                                </Badge>
                                            )}
                                            {scene.isDraft && editorState.publishedScene?.id === scene.id && (
                                                <Badge variant="outline" className="text-xs border-orange-500 text-orange-600">
                                                    Updated
                                                </Badge>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </Button>
                            {!previewMode && (
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                            <MoreVertical className="w-4 h-4" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem onClick={() => setEditingSceneId(scene.id)}>
                                            <Edit2 className="w-4 h-4 mr-2" />
                                            Rename
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => duplicateScene(scene.id)}>
                                            <CopyIcon className="w-4 h-4 mr-2" />
                                            Duplicate
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem 
                                            onClick={() => deleteScene(scene.id)}
                                            className="text-destructive focus:text-destructive"
                                        >
                                            <Trash2 className="w-4 h-4 mr-2" />
                                            Delete
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            )}
                        </div>
                    ))}
                </div>
                {!previewMode && (
                    <Button onClick={createNewScene} className="w-full" size="sm">
                        <Plus className="w-4 h-4 mr-2" />
                        Add Scene
                    </Button>
                )}
        </div>
    );
};