import React from 'react';
import { <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Element, TextElement, ImageElement, VideoElement, AudioElement, LiveStreamElement } from '@shared/types';

interface ElementPropertiesProps {
    selectedElement: Element;
    updateTextElement: (id: string, updates: any) => void;
    updateImageElement: (id: string, updates: any) => void;
    updateVideoElement: (id: string, updates: any) => void;
    updateAudioElement: (id: string, updates: any) => void;
    updateLiveStreamElement: (id: string, updates: any) => void;
}

export const ElementProperties: React.FC<ElementPropertiesProps> = ({
    selectedElement,
    updateTextElement,
    updateImageElement,
    updateVideoElement,
    updateAudioElement,
    updateLiveStreamElement
}) => {
    // Guard against missing transform
    if (!selectedElement?.transform) {
        return (
            <div className="p-4 text-center text-muted-foreground">
                <p>Element transform data is missing</p>
            </div>
        );
    }

    return (
        <div className="p-4">
            <CardHeader className="px-0 pb-3">
                <CardTitle className="text-sm font-semibold uppercase tracking-wider text-muted-foreground">
                    Properties
                </CardTitle>
            </CardHeader>
            <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <label className="text-sm font-medium">
                            Width: {Math.round(selectedElement.transform.width * (selectedElement.transform.scaleX || 1))}px
                        </label>
                        <div className="text-xs text-muted-foreground">
                            Position: {Math.round(selectedElement.transform.x)}px
                        </div>
                    </div>
                    <div className="space-y-2">
                        <label className="text-sm font-medium">
                            Height: {Math.round(selectedElement.transform.height * (selectedElement.transform.scaleY || 1))}px
                        </label>
                        <div className="text-xs text-muted-foreground">
                            Position: {Math.round(selectedElement.transform.y)}px
                        </div>
                    </div>
                </div>
                
                {selectedElement.type === 'text' && (
                    <div className="space-y-4 border-t pt-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Content</label>
                            <Input 
                                value={(selectedElement as TextElement).content}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateTextElement(selectedElement.id, { content: e.target.value })}
                                placeholder="Enter text..."
                            />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Font</label>
                                <Select 
                                    value={(selectedElement as TextElement).style.fontFamily}
                                    onValueChange={(value: string) => updateTextElement(selectedElement.id, { 
                                        style: { ...(selectedElement as TextElement).style, fontFamily: value }
                                    })}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent className="bg-white dark:bg-gray-800">
                                        <SelectItem value="Arial">Arial</SelectItem>
                                        <SelectItem value="Helvetica">Helvetica</SelectItem>
                                        <SelectItem value="Times New Roman">Times New Roman</SelectItem>
                                        <SelectItem value="Georgia">Georgia</SelectItem>
                                        <SelectItem value="Verdana">Verdana</SelectItem>
                                        <SelectItem value="Courier New">Courier New</SelectItem>
                                        <SelectItem value="Impact">Impact</SelectItem>
                                        <SelectItem value="Comic Sans MS">Comic Sans MS</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Size</label>
                                <Input 
                                    type="number"
                                    value={(selectedElement as TextElement).style.fontSize}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateTextElement(selectedElement.id, { 
                                        style: { ...(selectedElement as TextElement).style, fontSize: parseInt(e.target.value) || 12 }
                                    })}
                                    min="8"
                                    max="200"
                                />
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Color</label>
                                <Input 
                                    type="color"
                                    value={(selectedElement as TextElement).style.color}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateTextElement(selectedElement.id, { 
                                        style: { ...(selectedElement as TextElement).style, color: e.target.value }
                                    })}
                                    className="h-9 p-1"
                                />
                            </div>
                            
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Weight</label>
                                <Select 
                                    value={(selectedElement as TextElement).style.fontWeight}
                                    onValueChange={(value: string) => updateTextElement(selectedElement.id, { 
                                        style: { ...(selectedElement as TextElement).style, fontWeight: value as TextElement['style']['fontWeight'] }
                                    })}
                                >
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent className="bg-white dark:bg-gray-800">
                                        <SelectItem value="normal">Normal</SelectItem>
                                        <SelectItem value="bold">Bold</SelectItem>
                                        <SelectItem value="lighter">Light</SelectItem>
                                        <SelectItem value="bolder">Extra Bold</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Alignment</label>
                            <Select 
                                value={(selectedElement as TextElement).style.textAlign}
                                onValueChange={(value: string) => updateTextElement(selectedElement.id, { 
                                    style: { ...(selectedElement as TextElement).style, textAlign: value as 'left' | 'center' | 'right' }
                                })}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent className="bg-white dark:bg-gray-800">
                                    <SelectItem value="left">Left</SelectItem>
                                    <SelectItem value="center">Center</SelectItem>
                                    <SelectItem value="right">Right</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                )}
                
                {selectedElement.type === 'image' && (
                    <div className="space-y-4 border-t pt-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Source</label>
                            <div className="text-xs text-muted-foreground">
                                {(selectedElement as ImageElement).alt}
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Opacity</label>
                                <Slider
                                    value={[((selectedElement as ImageElement).style.opacity || 1) * 100]}
                                    onValueChange={(value: number[]) => updateImageElement(selectedElement.id, {
                                        style: { ...(selectedElement as ImageElement).style, opacity: value[0] / 100 }
                                    })}
                                    min={0}
                                    max={100}
                                    step={1}
                                />
                                <div className="text-xs text-muted-foreground">
                                    {Math.round(((selectedElement as ImageElement).style.opacity || 1) * 100)}%
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Border Radius</label>
                                <Input
                                    type="number"
                                    value={(selectedElement as ImageElement).style.borderRadius || 0}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateImageElement(selectedElement.id, {
                                        style: { ...(selectedElement as ImageElement).style, borderRadius: parseInt(e.target.value) || 0 }
                                    })}
                                    min="0"
                                    max="50"
                                />
                            </div>
                        </div>
                    </div>
                )}
                
                {selectedElement.type === 'video' && (
                    <div className="space-y-4 border-t pt-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Source</label>
                            <div className="text-xs text-muted-foreground">
                                {(selectedElement as VideoElement).name}
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Opacity</label>
                                <Slider
                                    value={[((selectedElement as VideoElement).style.opacity || 1) * 100]}
                                    onValueChange={(value: number[]) => updateVideoElement(selectedElement.id, {
                                        style: { ...(selectedElement as VideoElement).style, opacity: value[0] / 100 }
                                    })}
                                    min={0}
                                    max={100}
                                    step={1}
                                />
                                <div className="text-xs text-muted-foreground">
                                    {Math.round(((selectedElement as VideoElement).style.opacity || 1) * 100)}%
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Border Radius</label>
                                <Input
                                    type="number"
                                    value={(selectedElement as VideoElement).style.borderRadius || 0}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateVideoElement(selectedElement.id, {
                                        style: { ...(selectedElement as VideoElement).style, borderRadius: parseInt(e.target.value) || 0 }
                                    })}
                                    min="0"
                                    max="50"
                                />
                            </div>
                        </div>
                        
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="autoplay"
                                    checked={(selectedElement as VideoElement).autoplay}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateVideoElement(selectedElement.id, {
                                        autoplay: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="autoplay" className="text-sm font-medium">Autoplay</label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="loop"
                                    checked={(selectedElement as VideoElement).loop}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateVideoElement(selectedElement.id, {
                                        loop: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="loop" className="text-sm font-medium">Loop</label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="muted"
                                    checked={(selectedElement as VideoElement).muted}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateVideoElement(selectedElement.id, {
                                        muted: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="muted" className="text-sm font-medium">Muted</label>
                            </div>
                        </div>
                    </div>
                )}

                {selectedElement.type === 'audio' && (
                    <div className="space-y-4 border-t pt-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Source</label>
                            <div className="text-xs text-muted-foreground">
                                {(selectedElement as AudioElement).name}
                            </div>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Volume</label>
                                <Slider
                                    value={[(selectedElement as AudioElement).volume * 100]}
                                    onValueChange={(value: number[]) => updateAudioElement(selectedElement.id, {
                                        volume: value[0] / 100
                                    })}
                                    min={0}
                                    max={100}
                                    step={1}
                                />
                                <div className="text-xs text-muted-foreground">
                                    {Math.round((selectedElement as AudioElement).volume * 100)}%
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Opacity</label>
                                <Slider
                                    value={[((selectedElement as AudioElement).style.opacity || 1) * 100]}
                                    onValueChange={(value: number[]) => updateAudioElement(selectedElement.id, {
                                        style: { ...(selectedElement as AudioElement).style, opacity: value[0] / 100 }
                                    })}
                                    min={0}
                                    max={100}
                                    step={1}
                                />
                                <div className="text-xs text-muted-foreground">
                                    {Math.round(((selectedElement as AudioElement).style.opacity || 1) * 100)}%
                                </div>
                            </div>
                        </div>
                        
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="audioAutoplay"
                                    checked={(selectedElement as AudioElement).autoplay}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAudioElement(selectedElement.id, {
                                        autoplay: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="audioAutoplay" className="text-sm font-medium">Autoplay</label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="audioLoop"
                                    checked={(selectedElement as AudioElement).loop}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAudioElement(selectedElement.id, {
                                        loop: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="audioLoop" className="text-sm font-medium">Loop</label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="audioMuted"
                                    checked={(selectedElement as AudioElement).muted}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateAudioElement(selectedElement.id, {
                                        muted: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="audioMuted" className="text-sm font-medium">Muted</label>
                            </div>
                        </div>
                    </div>
                )}

                {selectedElement.type === 'livestream' && (
                    <div className="space-y-4 border-t pt-4">
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Stream URL</label>
                            <Input
                                type="url"
                                value={(selectedElement as LiveStreamElement).originalUrl}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateLiveStreamElement(selectedElement.id, {
                                    originalUrl: e.target.value,
                                    // For simple URLs, use them directly; for complex ones user should use MediaDownloader
                                    streamUrl: e.target.value ? `http://localhost:3001/stream/${encodeURIComponent(e.target.value)}` : ''
                                })}
                                placeholder="Enter livestream URL (YouTube, Twitch, Kick, etc.)"
                                className="text-xs"
                            />
                        </div>
                        
                        <div className="grid grid-cols-2 gap-3">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Volume</label>
                                <Slider
                                    value={[(selectedElement as LiveStreamElement).volume * 100]}
                                    onValueChange={(value: number[]) => updateLiveStreamElement(selectedElement.id, {
                                        volume: value[0] / 100
                                    })}
                                    min={0}
                                    max={100}
                                    step={1}
                                />
                                <div className="text-xs text-muted-foreground">
                                    {Math.round((selectedElement as LiveStreamElement).volume * 100)}%
                                </div>
                            </div>
                            
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Opacity</label>
                                <Slider
                                    value={[((selectedElement as LiveStreamElement).style.opacity || 1) * 100]}
                                    onValueChange={(value: number[]) => updateLiveStreamElement(selectedElement.id, {
                                        style: { ...(selectedElement as LiveStreamElement).style, opacity: value[0] / 100 }
                                    })}
                                    min={0}
                                    max={100}
                                    step={1}
                                />
                                <div className="text-xs text-muted-foreground">
                                    {Math.round(((selectedElement as LiveStreamElement).style.opacity || 1) * 100)}%
                                </div>
                            </div>
                        </div>
                        
                        <div className="space-y-2">
                            <label className="text-sm font-medium">Border Radius</label>
                            <Input
                                type="number"
                                value={(selectedElement as LiveStreamElement).style.borderRadius || 0}
                                onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateLiveStreamElement(selectedElement.id, {
                                    style: { ...(selectedElement as LiveStreamElement).style, borderRadius: parseInt(e.target.value) || 0 }
                                })}
                                min="0"
                                max="50"
                            />
                        </div>
                        
                        <div className="space-y-3">
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="livestreamAutoplay"
                                    checked={(selectedElement as LiveStreamElement).autoplay}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateLiveStreamElement(selectedElement.id, {
                                        autoplay: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="livestreamAutoplay" className="text-sm font-medium">Autoplay</label>
                            </div>
                            
                            <div className="flex items-center space-x-2">
                                <input
                                    type="checkbox"
                                    id="livestreamMuted"
                                    checked={(selectedElement as LiveStreamElement).muted}
                                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => updateLiveStreamElement(selectedElement.id, {
                                        muted: e.target.checked
                                    })}
                                    className="rounded"
                                />
                                <label htmlFor="livestreamMuted" className="text-sm font-medium">Muted</label>
                            </div>
                        </div>
                        
                        <div className="text-xs text-red-500">
                            🔴 Live stream - properties update in real-time
                        </div>
                    </div>
                )}
                
                <div className="text-xs text-muted-foreground border-t pt-2">
                    💡 Drag the element to move it or drag the resize handle to change its size
                </div>
            </div>
        </div>
    );
};