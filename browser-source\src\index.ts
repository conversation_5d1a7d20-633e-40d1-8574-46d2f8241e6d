import { Scene, Element, WebSocketMessage } from '../../shared/types';

export interface Env {
  BROWSER_SOURCE_STATE: DurableObjectNamespace;
  MEDIA_BUCKET: R2Bucket;
}

export class BrowserSourceState {
  private state: DurableObjectState;
  private currentScene: Scene | null = null;
  private connections: Set<WebSocket> = new Set();

  constructor(state: DurableObjectState) {
    this.state = state;
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    
    if (url.pathname === '/websocket') {
      return this.handleWebSocket(request);
    }

    return new Response('Not found', { status: 404 });
  }

  private async handleWebSocket(request: Request): Promise<Response> {
    const upgradeHeader = request.headers.get('Upgrade');
    if (!upgradeHeader || upgradeHeader !== 'websocket') {
      return new Response('Expected Upgrade: websocket', { status: 426 });
    }

    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    this.connections.add(server);

    server.accept();

    // Send current scene to new connection
    if (this.currentScene) {
      server.send(JSON.stringify({
        type: 'update_scene',
        scene: this.currentScene
      }));
    }

    server.addEventListener('message', async (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data as string);
        await this.handleMessage(message, server);
      } catch (error) {
        console.error('Error handling WebSocket message:', error);
      }
    });

    server.addEventListener('close', () => {
      this.connections.delete(server);
    });

    return new Response(null, { status: 101, webSocket: client });
  }

  private async handleMessage(message: WebSocketMessage, sender: WebSocket) {
    switch (message.type) {
      case 'update_scene':
        // Only handle published scenes for browser source
        if (!message.scene.isDraft) {
          this.currentScene = message.scene;
          await this.state.storage.put('currentScene', this.currentScene);
          this.broadcast(message, sender);
        }
        break;

      case 'publish_scene':
        // Handle published scenes
        this.currentScene = message.scene;
        await this.state.storage.put('currentScene', this.currentScene);
        // Broadcast as update_scene to browser source
        this.broadcast({ type: 'update_scene', scene: message.scene }, sender);
        break;

      case 'set_active_scene':
        // This would typically load a different scene
        // For now, just broadcast the message
        this.broadcast(message, sender);
        break;

      case 'refresh_stream_url':
        // Handle stream URL refresh request from browser source
        await this.handleStreamUrlRefresh(message, sender);
        break;

      default:
        console.log('Unknown message type:', message);
    }
  }

  private async handleStreamUrlRefresh(message: any, sender: WebSocket) {
    try {
      console.log('Handling stream URL refresh request:', message);
      
      // Forward the refresh request to the editor
      // The editor will extract a new stream URL and send it back
      this.broadcast({
        type: 'refresh_stream_url_request',
        elementId: message.elementId,
        originalUrl: message.originalUrl
      }, sender);
      
    } catch (error) {
      console.error('Error handling stream URL refresh:', error);
      
      // Send error response back to browser source
      sender.send(JSON.stringify({
        type: 'refresh_stream_url_response',
        success: false,
        elementId: message.elementId,
        error: 'Failed to refresh stream URL'
      }));
    }
  }

  private broadcast(message: WebSocketMessage, sender?: WebSocket) {
    const messageStr = JSON.stringify(message);
    for (const connection of this.connections) {
      if (connection !== sender) {
        connection.send(messageStr);
      }
    }
  }
}

async function handleUpload(request: Request, env: Env, corsHeaders: Record<string, string>): Promise<Response> {
  const formData = await request.formData();
  const file = formData.get('file') as File;
  const filename = formData.get('filename') as string;

  if (!file || !filename) {
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Missing file or filename' 
    }), { 
      status: 400, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    });
  }

  // Generate unique filename with timestamp
  const timestamp = Date.now();
  const extension = filename.split('.').pop();
  const uniqueFilename = `${timestamp}-${Math.random().toString(36).substring(2)}.${extension}`;

  try {
    // Upload to R2
    await env.MEDIA_BUCKET.put(uniqueFilename, file.stream(), {
      httpMetadata: {
        contentType: file.type,
      },
    });

    // Return the public URL (using the worker's domain + /media/ prefix)
    const url = new URL(request.url);
    const publicUrl = `${url.origin}/media/${uniqueFilename}`;

    return new Response(JSON.stringify({ 
      success: true, 
      url: publicUrl 
    }), { 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    });
  } catch (error) {
    console.error('R2 upload error:', error);
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Failed to upload to storage' 
    }), { 
      status: 500, 
      headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
    });
  }
}

export default {
  async fetch(request: Request, env: Env): Promise<Response> {
    const url = new URL(request.url);
    
    // CORS headers for API endpoints
    const corsHeaders = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }
    
    if (url.pathname === '/') {
      return new Response(getBrowserSourceHTML(), {
        headers: { 'Content-Type': 'text/html' }
      });
    }

    if (url.pathname === '/websocket') {
      // Create/get durable object instance
      const id = env.BROWSER_SOURCE_STATE.idFromName('global');
      const durableObject = env.BROWSER_SOURCE_STATE.get(id);
      return durableObject.fetch(request);
    }

    // Upload API endpoint
    if (url.pathname === '/api/upload' && request.method === 'POST') {
      try {
        return await handleUpload(request, env, corsHeaders);
      } catch (error) {
        console.error('Upload error:', error);
        return new Response(JSON.stringify({ 
          success: false, 
          error: error instanceof Error ? error.message : 'Upload failed' 
        }), { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        });
      }
    }

    // Media serving endpoint from R2
    if (url.pathname.startsWith('/media/')) {
      const filename = url.pathname.slice(7); // Remove '/media/' prefix
      
      try {
        const object = await env.MEDIA_BUCKET.get(filename);
        
        if (!object) {
          return new Response('File not found', { status: 404 });
        }

        const headers = new Headers();
        object.writeHttpMetadata(headers);
        headers.set('etag', object.httpEtag);
        headers.set('cache-control', 'public, max-age=31536000'); // 1 year cache
        headers.set('access-control-allow-origin', '*');

        return new Response(object.body, { headers });
      } catch (error) {
        console.error('Error serving media file:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    }

    return new Response('Not found', { status: 404 });
  }
};

function getBrowserSourceHTML(): string {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OBS Browser Source</title>
    <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: transparent;
            font-family: Arial, sans-serif;
            width: 1920px;
            height: 1080px;
            overflow: hidden;
        }
        
        .canvas {
            position: relative;
            width: 100%;
            height: 100%;
        }
        
        .element {
            position: absolute;
        }
        
        .text-element {
            white-space: nowrap;
        }
        
        .image-element,
        .video-element {
            display: block;
        }
    </style>
</head>
<body>
    <div id="canvas" class="canvas"></div>
    
    <script>
        const canvas = document.getElementById('canvas');
        let currentScene = null;
        
        // WebSocket connection to receive updates - use secure protocol for HTTPS pages
        const protocol = location.protocol === 'https:' ? 'wss:' : 'ws:';
        const ws = new WebSocket(\`\${protocol}//\${location.host}/websocket\`);
        
        ws.onopen = function() {
            console.log('WebSocket connected');
        };
        
        ws.onmessage = function(event) {
            const message = JSON.parse(event.data);
            handleMessage(message);
        };
        
        ws.onerror = function(error) {
            console.error('WebSocket error:', error);
        };
        
        ws.onclose = function() {
            console.log('WebSocket disconnected');
            // Try to reconnect after 3 seconds
            setTimeout(() => {
                location.reload();
            }, 3000);
        };
        
        function handleMessage(message) {
            switch (message.type) {
                case 'update_scene':
                    currentScene = message.scene;
                    renderScene(currentScene);
                    break;
                    
                case 'set_active_scene':
                    // Scene switching logic would go here
                    console.log('Switch to scene:', message.sceneId);
                    break;
                    
                case 'refresh_stream_url_response':
                    // Handle refreshed stream URL
                    if (message.success && message.elementId && message.newStreamUrl) {
                        console.log('Received refreshed stream URL for element:', message.elementId);
                        refreshElementStreamUrl(message.elementId, message.newStreamUrl);
                    } else {
                        console.error('Failed to refresh stream URL:', message.error);
                    }
                    break;
                    
                default:
                    console.log('Unknown message type:', message.type);
            }
        }
        
        function refreshElementStreamUrl(elementId, newStreamUrl) {
            if (!currentScene) return;
            
            // Find the element in the current scene
            const element = currentScene.elements.find(el => el.id === elementId);
            if (!element || element.type !== 'livestream') {
                console.error('Element not found or not a livestream:', elementId);
                return;
            }
            
            // Find the video element in the DOM
            const videoElements = canvas.querySelectorAll('video');
            for (const video of videoElements) {
                if (video._hlsInstance && video._elementId === elementId) {
                    console.log('Refreshing HLS stream URL for element:', elementId);
                    
                    // Update the stream URL and reload
                    element.streamUrl = newStreamUrl;
                    video._hlsInstance.loadSource(newStreamUrl);
                    video._refreshAttempts = 0; // Reset refresh attempts
                    
                    console.log('HLS stream URL refreshed successfully');
                    break;
                }
            }
        }
        
        function renderScene(scene) {
            if (!scene) return;
            
            // Clean up existing HLS instances before clearing canvas
            const existingVideos = canvas.querySelectorAll('video');
            existingVideos.forEach(video => {
                if (video._hlsInstance) {
                    video._hlsInstance.destroy();
                    video._hlsInstance = null;
                }
            });
            
            // Clear canvas
            canvas.innerHTML = '';
            
            // Sort elements by z-index
            const sortedElements = [...scene.elements].sort((a, b) => a.zIndex - b.zIndex);
            
            // Render each element
            sortedElements.forEach(element => {
                const elementDiv = createElement(element);
                if (elementDiv) {
                    canvas.appendChild(elementDiv);
                }
            });
        }
        
        function createElement(element) {
            const div = document.createElement('div');
            div.className = 'element';
            div.style.position = 'absolute';
            div.style.left = element.transform.x + 'px';
            div.style.top = element.transform.y + 'px';
            div.style.width = element.transform.width + 'px';
            div.style.height = element.transform.height + 'px';
            div.style.zIndex = element.zIndex;
            
            if (element.transform.rotation) {
                div.style.transform = \`rotate(\${element.transform.rotation}deg)\`;
            }
            
            switch (element.type) {
                case 'text':
                    div.className += ' text-element';
                    div.textContent = element.content;
                    div.style.fontSize = element.style.fontSize + 'px';
                    div.style.fontFamily = element.style.fontFamily;
                    div.style.color = element.style.color;
                    div.style.fontWeight = element.style.fontWeight;
                    div.style.textAlign = element.style.textAlign;
                    
                    if (element.style.textShadow) {
                        div.style.textShadow = element.style.textShadow;
                    }
                    if (element.style.backgroundColor) {
                        div.style.backgroundColor = element.style.backgroundColor;
                    }
                    if (element.style.padding) {
                        div.style.padding = element.style.padding + 'px';
                    }
                    if (element.style.borderRadius) {
                        div.style.borderRadius = element.style.borderRadius + 'px';
                    }
                    break;
                    
                case 'image':
                    const img = document.createElement('img');
                    img.src = element.src;
                    img.alt = element.alt || '';
                    img.className = 'image-element';
                    img.style.width = '100%';
                    img.style.height = '100%';
                    img.style.objectFit = 'contain';
                    
                    if (element.style.borderRadius) {
                        img.style.borderRadius = element.style.borderRadius + 'px';
                    }
                    if (element.style.opacity) {
                        img.style.opacity = element.style.opacity;
                    }
                    if (element.style.filter) {
                        img.style.filter = element.style.filter;
                    }
                    
                    div.appendChild(img);
                    break;
                    
                case 'video':
                    const video = document.createElement('video');
                    video.src = element.src;
                    video.className = 'video-element';
                    video.style.width = '100%';
                    video.style.height = '100%';
                    video.style.objectFit = 'contain';
                    video.autoplay = element.autoplay;
                    video.loop = element.loop;
                    video.muted = element.muted;
                    
                    if (element.style.borderRadius) {
                        video.style.borderRadius = element.style.borderRadius + 'px';
                    }
                    if (element.style.opacity) {
                        video.style.opacity = element.style.opacity;
                    }
                    if (element.style.filter) {
                        video.style.filter = element.style.filter;
                    }
                    
                    div.appendChild(video);
                    break;
                    
                case 'audio':
                    const audio = document.createElement('audio');
                    audio.src = element.src;
                    audio.autoplay = element.autoplay;
                    audio.loop = element.loop;
                    audio.muted = element.muted;
                    audio.volume = element.volume || 1.0;
                    audio.controls = false; // No visual controls in browser source
                    
                    if (element.style.opacity) {
                        audio.style.opacity = element.style.opacity;
                    }
                    
                    div.appendChild(audio);
                    break;
                    
                case 'livestream':
                    const livestreamVideo = document.createElement('video');
                    livestreamVideo.className = 'video-element';
                    livestreamVideo.style.width = '100%';
                    livestreamVideo.style.height = '100%';
                    livestreamVideo.style.objectFit = 'contain';
                    livestreamVideo.autoplay = element.autoplay;
                    livestreamVideo.muted = element.muted;
                    livestreamVideo.volume = element.volume || 1.0;
                    livestreamVideo.controls = false; // No visual controls in browser source
                    livestreamVideo.crossorigin = 'anonymous'; // For CORS
                    
                    if (element.style.borderRadius) {
                        livestreamVideo.style.borderRadius = element.style.borderRadius + 'px';
                    }
                    if (element.style.opacity) {
                        livestreamVideo.style.opacity = element.style.opacity;
                    }
                    if (element.style.filter) {
                        livestreamVideo.style.filter = element.style.filter;
                    }
                    
                    // Use HLS.js for HLS streams, fallback to native for other formats
                    if (window.Hls && window.Hls.isSupported() && element.streamUrl.includes('.m3u8')) {
                        const hls = new window.Hls({
                            enableWorker: false, // Disable worker for better compatibility in OBS
                            lowLatencyMode: true, // Enable low latency for live streams
                            backBufferLength: 10, // Keep small buffer for live content
                            liveSyncDuration: 1, // Reduce sync duration for lower latency
                            liveMaxLatencyDuration: 5, // Max latency before seeking
                            maxMaxBufferLength: 10, // Keep buffer small for live content
                        });
                        
                        hls.loadSource(element.streamUrl);
                        hls.attachMedia(livestreamVideo);
                        
                        hls.on(window.Hls.Events.MANIFEST_PARSED, function() {
                            if (element.autoplay) {
                                livestreamVideo.play().catch(e => console.log('Autoplay prevented:', e));
                            }
                        });
                        
                        let refreshAttempts = 0;
                        const maxRefreshAttempts = 3;
                        
                        hls.on(window.Hls.Events.ERROR, function(event, data) {
                            console.error('HLS error:', data);
                            
                            // Handle 403 errors specifically for live streams
                            if (data.response && data.response.code === 403 && data.frag) {
                                console.log('403 error detected, stream URL may have expired');
                                
                                if (refreshAttempts < maxRefreshAttempts) {
                                    refreshAttempts++;
                                    console.log(\`Attempting stream URL refresh (\${refreshAttempts}/\${maxRefreshAttempts})\`);
                                    
                                    // Send message to refresh the stream URL
                                    if (ws && ws.readyState === WebSocket.OPEN) {
                                        ws.send(JSON.stringify({
                                            type: 'refresh_stream_url',
                                            elementId: element.id,
                                            originalUrl: element.originalUrl
                                        }));
                                    }
                                    
                                    // Wait a bit before retrying
                                    setTimeout(() => {
                                        hls.startLoad();
                                    }, 2000);
                                } else {
                                    console.log('Max refresh attempts reached, giving up');
                                }
                                return;
                            }
                            
                            if (data.fatal) {
                                switch(data.type) {
                                    case window.Hls.ErrorTypes.NETWORK_ERROR:
                                        console.log('Network error, trying to recover...');
                                        hls.startLoad();
                                        break;
                                    case window.Hls.ErrorTypes.MEDIA_ERROR:
                                        console.log('Media error, trying to recover...');
                                        hls.recoverMediaError();
                                        break;
                                    default:
                                        console.log('Fatal error, destroying HLS instance');
                                        hls.destroy();
                                        break;
                                }
                            }
                        });
                        
                        // Store HLS instance and refresh info for cleanup
                        livestreamVideo._hlsInstance = hls;
                        livestreamVideo._refreshAttempts = 0;
                        livestreamVideo._elementId = element.id;
                    } else if (livestreamVideo.canPlayType('application/vnd.apple.mpegurl')) {
                        // Native HLS support (Safari, some mobile browsers)
                        livestreamVideo.src = element.streamUrl;
                        if (element.autoplay) {
                            livestreamVideo.play().catch(e => console.log('Autoplay prevented:', e));
                        }
                    } else {
                        // Fallback for non-HLS streams
                        livestreamVideo.src = element.streamUrl;
                        if (element.autoplay) {
                            livestreamVideo.play().catch(e => console.log('Autoplay prevented:', e));
                        }
                    }
                    
                    div.appendChild(livestreamVideo);
                    break;
            }
            
            return div;
        }
    </script>
</body>
</html>`;
}
