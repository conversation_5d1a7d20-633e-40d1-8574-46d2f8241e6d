import { useState, useCallback } from 'react';
import { Element } from '@shared/types';

export const useElementSelection = () => {
    const [selectedElement, setSelectedElement] = useState<Element | null>(null);
    const [copiedElement, setCopiedElement] = useState<Element | null>(null);
    const [editingSceneId, setEditingSceneId] = useState<string | null>(null);
    const [editingElementId, setEditingElementId] = useState<string | null>(null);
    const [previewMode, setPreviewMode] = useState(false);

    const copyElement = useCallback(() => {
        if (selectedElement) {
            setCopiedElement(JSON.parse(JSON.stringify(selectedElement)));
        }
    }, [selectedElement]);

    const clearSelection = useCallback(() => {
        setSelectedElement(null);
    }, []);

    const selectElement = useCallback((element: Element | null) => {
        setSelectedElement(element);
    }, []);

    return {
        selectedElement,
        setSelectedElement: selectElement,
        copiedElement,
        setCopiedElement,
        editingSceneId,
        setEditingSceneId,
        editingElementId,
        setEditingElementId,
        previewMode,
        setPreviewMode,
        copyElement,
        clearSelection
    };
};
