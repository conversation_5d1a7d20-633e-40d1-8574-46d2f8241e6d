import React, { useState, useEffect } from 'react';
import { CommandPalette } from './CommandPalette';
import { SceneTabs } from './SceneTabs';
import { FloatingProperties } from './FloatingProperties';
import { Canvas } from '../editor/Canvas';
import { HoverZones } from './HoverZones';
import { MediaDownloader } from '../MediaDownloader';
import { Button } from '@/components/ui/button';
import { Play, Square, Download } from 'lucide-react';
import type { Scene, Element, EditorState } from '@shared/types';

interface MinimalLayoutProps {
    // Props for minimal UI layout
    editorState: EditorState;
    activeScene: Scene | undefined;
    selectedElement: Element | null;
    previewMode: boolean;
    setPreviewMode: (mode: boolean) => void;
    
    // Scene management
    selectScene: (sceneId: string) => void;
    createNewScene: () => void;
    deleteScene: (sceneId: string) => void;
    duplicateScene: (sceneId: string) => void;
    renameScene: (sceneId: string, name: string) => void;
    
    // Element management
    setSelectedElement: (element: Element | null) => void;
    clearSelection: () => void;
    deleteElement: (elementId: string) => void;
    toggleElementVisibility: (elementId: string) => void;
    renameElement: (elementId: string, name: string) => void;
    
    // Element creation
    addTextElement: () => void;
    addImageElement: () => void;
    addVideoElement: () => void;
    addAudioElement: () => void;
    addLiveStreamElement: () => void;
    
    // Add missing live stream function with extracted data
    addLiveStreamElementWithData: (streamData: {
        originalUrl: string;
        streamUrl: string;
        platform: string;
        title: string;
    }) => void;
    
    // Element updates
    updateTextElement: (elementId: string, updates: any) => void;
    updateImageElement: (elementId: string, updates: any) => void;
    updateVideoElement: (elementId: string, updates: any) => void;
    updateAudioElement: (elementId: string, updates: any) => void;
    updateLiveStreamElement: (elementId: string, updates: any) => void;
    updateElementTransform: (elementId: string, transform: any) => void;
    
    // Layering
    bringToFront: (elementId: string) => void;
    sendToBack: (elementId: string) => void;
    bringForward: (elementId: string) => void;
    sendBackward: (elementId: string) => void;
    
    // Grouping
    createGroup: (elementIds: string[]) => void;
    ungroupElements: (groupId: string) => void;
    addToGroup: (groupId: string, elementIds: string[]) => void;
    removeFromGroup: (groupId: string, elementIds: string[]) => void;
    toggleGroupVisibility: (groupId: string) => void;
    toggleGroupLock: (groupId: string) => void;
    renameGroup: (groupId: string, name: string) => void;
    
    // Canvas & interaction
    canvasSize: { width: number; height: number };
    zoomLevel: number | 'fit';
    calculateScale: () => number;
    isDragging: boolean;
    isPanning: boolean;
    setIsDragging: (dragging: boolean) => void;
    handleCanvasPanStart: (e: any) => void;
    handleCanvasPanMove: (e: any) => void;
    handleCanvasPanEnd: (e: any) => void;
    
    // Actions
    onSave: () => void;
    onLoad: () => void;
    onCopy: () => void;
    onPaste: () => void;
    onDuplicate: () => void;
    onPublish: () => void;
    onCenter: () => void;
    onZoomChange: (zoom: number | 'fit') => void;
    copiedElement: Element | null;
    
}

export const MinimalLayout: React.FC<MinimalLayoutProps> = ({
    editorState,
    activeScene,
    selectedElement,
    previewMode,
    setPreviewMode,
    selectScene,
    createNewScene,
    deleteScene,
    duplicateScene,
    renameScene,
    setSelectedElement,
    clearSelection,
    deleteElement,
    toggleElementVisibility,
    renameElement,
    addTextElement,
    addImageElement,
    addVideoElement,
    addAudioElement,
    addLiveStreamElement,
    addLiveStreamElementWithData,
    updateTextElement,
    updateImageElement,
    updateVideoElement,
    updateAudioElement,
    updateLiveStreamElement,
    updateElementTransform,
    bringToFront,
    sendToBack,
    bringForward,
    sendBackward,
    createGroup,
    ungroupElements,
    addToGroup,
    removeFromGroup,
    toggleGroupVisibility,
    toggleGroupLock,
    renameGroup,
    canvasSize,
    zoomLevel,
    calculateScale,
    isDragging,
    isPanning,
    setIsDragging,
    handleCanvasPanStart,
    handleCanvasPanMove,
    handleCanvasPanEnd,
    onSave,
    onLoad,
    onCopy,
    onPaste,
    onDuplicate,
    onPublish,
    onCenter,
    onZoomChange,
    copiedElement
}) => {
    const [commandPaletteOpen, setCommandPaletteOpen] = useState(false);
    const [showHoverZones, setShowHoverZones] = useState(false);
    const [hoverZonesAnimating, setHoverZonesAnimating] = useState(false);
    const [showDownloader, setShowDownloader] = useState(false);
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [showPropertiesPanel, setShowPropertiesPanel] = useState(false);

    // Determine which scenes to show based on preview mode
    const scenesToShow = previewMode 
        ? (editorState.publishedScene ? [editorState.publishedScene] : editorState.scenes)
        : editorState.scenes;

    // Debug logging for preview mode
    console.log('MinimalLayout Debug:', {
        previewMode,
        publishedScene: editorState.publishedScene ? { id: editorState.publishedScene.id, name: editorState.publishedScene.name, isDraft: editorState.publishedScene.isDraft } : null,
        totalScenesCount: editorState.scenes.length,
        scenesToShowCount: scenesToShow.length,
        activeSceneId: activeScene?.id,
        activeSceneName: activeScene?.name,
        allScenes: editorState.scenes.map(s => ({ id: s.id, name: s.name, isDraft: s.isDraft })),
        scenesToShow: scenesToShow.map(s => ({ id: s.id, name: s.name, isDraft: s.isDraft }))
    });

    // Type adapters for Canvas props
    const handleSetSelectedElement = (element: Element | null) => {
        setSelectedElement(element);
    };

    const convertedZoomLevel = typeof zoomLevel === 'number' ? 'fit' as const : zoomLevel;

    const handleCanvasPanEndWrapper = () => {
        handleCanvasPanEnd({} as any);
    };

    // Keyboard shortcuts for command palette
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                e.preventDefault();
                setCommandPaletteOpen(true);
            }
            if (e.key === 'Escape') {
                setCommandPaletteOpen(false);
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, []);

    // Show hover zones when mouse moves to edges
    useEffect(() => {
        let hideTimeout: NodeJS.Timeout;
        
        const checkShouldHide = (clientX?: number, clientY?: number) => {
            const edgeThreshold = 50;
            // Use current mouse position or get it from the last mouse event
            const x = clientX ?? window.mouseX ?? 0;
            const y = clientY ?? window.mouseY ?? 0;
            const { innerWidth, innerHeight } = window;
            
            const nearEdge = x < edgeThreshold || 
                           x > innerWidth - edgeThreshold || 
                           y < edgeThreshold || 
                           y > innerHeight - edgeThreshold;
            
            if (nearEdge) {
                // Show immediately when near edge - always override any existing state
                clearTimeout(hideTimeout);
                setShowHoverZones(true);
                setHoverZonesAnimating(false);
            } else if (showHoverZones && !isDropdownOpen) {
                // Start exit animation only if no dropdown is open
                clearTimeout(hideTimeout);
                setHoverZonesAnimating(true);
                
                // Hide after animation completes
                hideTimeout = setTimeout(() => {
                    setShowHoverZones(false);
                    setHoverZonesAnimating(false);
                }, 450); // Slightly longer than animation duration
            }
        };
        
        const handleMouseMove = (e: MouseEvent) => {
            // Store mouse position for later use
            (window as any).mouseX = e.clientX;
            (window as any).mouseY = e.clientY;
            checkShouldHide(e.clientX, e.clientY);
        };

        window.addEventListener('mousemove', handleMouseMove);
        
        // Check immediately when dropdown state changes
        if (!isDropdownOpen) {
            checkShouldHide();
        }
        
        return () => {
            window.removeEventListener('mousemove', handleMouseMove);
            clearTimeout(hideTimeout);
        };
    }, [showHoverZones, isDropdownOpen]);

    // Auto-hide properties panel when no element is selected or when scene changes
    useEffect(() => {
        if (!selectedElement) {
            setShowPropertiesPanel(false);
        }
    }, [selectedElement]);

    // Reset properties panel and clear selection when switching scenes
    useEffect(() => {
        setShowPropertiesPanel(false);
        clearSelection();
    }, [activeScene?.id, clearSelection]);

    return (
        <div className="h-screen w-screen bg-background text-foreground overflow-hidden relative">
            {/* Scene Tabs with integrated controls */}
            <SceneTabs
                scenes={scenesToShow}
                activeScene={activeScene}
                selectScene={selectScene}
                createNewScene={createNewScene}
                deleteScene={deleteScene}
                duplicateScene={duplicateScene}
                renameScene={renameScene}
                previewMode={previewMode}
                setPreviewMode={setPreviewMode}
                onSave={onSave}
                onLoad={onLoad}
                publishedScene={editorState.publishedScene}
                onPublish={onPublish}
            />

            {/* Full Canvas - 99% of screen */}
            <div className="absolute inset-0 pt-8">
                <Canvas
                    activeScene={activeScene}
                    selectedElement={previewMode ? null : (selectedElement || null)}
                    canvasSize={canvasSize}
                    zoomLevel={convertedZoomLevel}
                    calculateScale={calculateScale}
                    previewMode={previewMode}
                    isDragging={isDragging}
                    isPanning={isPanning}
                    setSelectedElement={previewMode ? () => {} : handleSetSelectedElement}
                    setIsDragging={setIsDragging}
                    updateElementTransform={previewMode ? () => {} : updateElementTransform}
                    handleCanvasPanStart={handleCanvasPanStart}
                    handleCanvasPanMove={handleCanvasPanMove}
                    handleCanvasPanEnd={handleCanvasPanEndWrapper}
                    containerStyle={{
                        overflow: 'auto',
                        overflowX: 'auto',
                        overflowY: 'auto',
                        width: '100%',
                        height: '100%'
                    }}
                />
            </div>

            {/* Floating Properties Panel - appears when toggled and element selected */}
            {selectedElement && !previewMode && showPropertiesPanel && (
                <FloatingProperties
                    element={selectedElement}
                    updateTextElement={updateTextElement}
                    updateImageElement={updateImageElement}
                    updateVideoElement={updateVideoElement}
                    updateAudioElement={updateAudioElement}
                    updateLiveStreamElement={updateLiveStreamElement}
                    updateElementTransform={updateElementTransform}
                    deleteElement={deleteElement}
                    renameElement={renameElement}
                    bringToFront={bringToFront}
                    sendToBack={sendToBack}
                    bringForward={bringForward}
                    sendBackward={sendBackward}
                />
            )}

            {/* Hover Zones - edge-triggered tool access */}
            {showHoverZones && (
                <HoverZones
                    onCenter={onCenter}
                    zoomLevel={zoomLevel}
                    onZoomChange={onZoomChange}
                    addTextElement={addTextElement}
                    addImageElement={addImageElement}
                    addVideoElement={addVideoElement}
                    addAudioElement={addAudioElement}
                    addLiveStreamElement={addLiveStreamElement}
                    onOpenDownloader={() => setShowDownloader(true)}
                    onZoomToFit={() => onZoomChange('fit')} // Use 'fit' string to reset zoom
                    isExiting={hoverZonesAnimating}
                    previewMode={previewMode}
                    onDropdownOpenChange={setIsDropdownOpen}
                    selectedElement={selectedElement}
                    showPropertiesPanel={showPropertiesPanel}
                    onTogglePropertiesPanel={() => setShowPropertiesPanel(!showPropertiesPanel)}
                />
            )}

            {/* Command Palette - Cmd+K for everything */}
            <CommandPalette
                isOpen={commandPaletteOpen}
                onClose={() => setCommandPaletteOpen(false)}
                editorState={{...editorState, scenes: scenesToShow}}
                activeScene={activeScene}
                selectedElement={selectedElement}
                // All action handlers
                selectScene={selectScene}
                createNewScene={createNewScene}
                deleteScene={deleteScene}
                addTextElement={addTextElement}
                addImageElement={addImageElement}
                addVideoElement={addVideoElement}
                addAudioElement={addAudioElement}
                addLiveStreamElement={addLiveStreamElement}
                deleteElement={deleteElement}
                onSave={onSave}
                onLoad={onLoad}
                onCopy={onCopy}
                onPaste={onPaste}
                onDuplicate={onDuplicate}
                onPublish={onPublish}
                onCenter={onCenter}
                onZoomToFit={() => onZoomChange('fit')} // Use 'fit' string to reset zoom
                onOpenDownloader={() => setShowDownloader(true)}
            />

            {/* Media Downloader - Floating panel */}
                        {/* Media Downloader - Centered floating panel */}
            {showDownloader && (
                <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-50 w-96 bg-background/95 backdrop-blur-sm border border-border/50 rounded-lg shadow-xl">
                    <div className="flex items-center justify-between p-3 border-b border-border/30">
                        <h3 className="text-sm font-medium">Download Media</h3>
                        <button
                            onClick={() => setShowDownloader(false)}
                            className="h-6 w-6 rounded-sm opacity-70 hover:opacity-100 hover:bg-accent flex items-center justify-center text-sm"
                        >
                            ×
                        </button>
                    </div>
                    <div className="p-3">
                        <MediaDownloader
                            isOpen={true}
                            onClose={() => setShowDownloader(false)}
                            onStreamExtracted={addLiveStreamElementWithData}
                        />
                    </div>
                </div>
            )}

            {/* Subtle help indicator */}
            <div className="absolute bottom-4 right-4 text-xs text-muted-foreground/60 bg-black/20 backdrop-blur-sm px-2 py-1 rounded">
                Press <kbd className="px-1 py-0.5 bg-muted/40 rounded text-xs">⌘K</kbd> for commands
            </div>
        </div>
    );
};