import { useState, useEffect, useCallback, useRef } from 'react';
import { EditorState, Scene, Element } from '@shared/types';
import { ElectronAPI } from '../../preload';

const api = (window as any).electronAPI as ElectronAPI;

export const useEditorState = () => {
    const [editorState, setEditorState] = useState<EditorState>({
        scenes: [],
        publishedScene: undefined,
        canvasSize: { width: 1920, height: 1080 }
    });

    const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

    useEffect(() => {
        api.getEditorState().then(state => {
            if (state) {
                setEditorState(state);
            }
        });
    }, []);

    const saveToHistory = useCallback((state: EditorState) => {
        // No-op: History functionality removed
    }, []);


    const debouncedApiUpdate = useCallback((scene: Scene) => {
        if (updateTimeoutRef.current) {
            clearTimeout(updateTimeoutRef.current);
        }
        updateTimeoutRef.current = setTimeout(async () => {
            await api.updateScene(scene);
            const freshState = await api.getEditorState();
            setEditorState(freshState);
        }, 100);
    }, []);

    const updateScene = useCallback((scene: Scene) => {
        const updatedState = {
            ...editorState,
            scenes: editorState.scenes.map(s => s.id === scene.id ? scene : s)
        };
        
        setEditorState(updatedState);
        debouncedApiUpdate(scene);
    }, [editorState, debouncedApiUpdate]);

    const selectScene = useCallback((sceneId: string) => {
        const updatedState = {
            ...editorState,
            activeSceneId: sceneId
        };
        
        setEditorState(updatedState);
        api.setActiveScene(sceneId);
    }, [editorState]);

    const refreshEditorState = useCallback(async () => {
        const freshState = await api.getEditorState();
        setEditorState(freshState);
        return freshState;
    }, []);


    return {
        editorState,
        setEditorState,
        saveToHistory,
        updateScene,
        selectScene,
        refreshEditorState
    };
};