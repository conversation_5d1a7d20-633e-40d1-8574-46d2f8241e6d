import { app } from 'electron';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid';

export class StorageService {
    private storageDir: string;
    private workerUrl: string;
    private useCloudStorage: boolean;

    constructor() {
        // Create storage directory in app data (fallback for local development)
        this.storageDir = path.join(app.getPath('userData'), 'obs-editor-media');
        
        // Determine storage mode based on environment
        // Use cloud storage if NODE_ENV is production, if app is packaged, or if PRODUCTION_WEBSOCKET_URL is set
        this.useCloudStorage = process.env.NODE_ENV === 'production' || 
                              app.isPackaged || 
                              !!process.env.PRODUCTION_WEBSOCKET_URL;
        
        console.log('=== Storage Service Debug ===');
        console.log('NODE_ENV:', process.env.NODE_ENV);
        console.log('app.isPackaged:', app.isPackaged);
        console.log('PRODUCTION_WEBSOCKET_URL:', !!process.env.PRODUCTION_WEBSOCKET_URL);
        console.log('useCloudStorage:', this.useCloudStorage);
        
        if (this.useCloudStorage) {
            // Use worker API for cloud storage (hardcoded for packaged app)
            this.workerUrl = 'https://obs-browser-source.cloudflare-okay454.workers.dev';
            console.log('✅ Storage service initialized with worker API:', this.workerUrl);
        } else {
            // Use local storage for development
            this.workerUrl = 'http://127.0.0.1:8787'; // Local worker dev server
            
            // Ensure storage directory exists for fallback
            if (!fs.existsSync(this.storageDir)) {
                fs.mkdirSync(this.storageDir, { recursive: true });
            }
            console.log('⚠️ Storage service initialized with local worker/storage');
        }
    }

    /**
     * Update the media server port for local development
     */
    updateBaseUrl(port: number): void {
        this.mediaServerPort = port;
        if (!this.useCloudStorage) {
            console.log(`Local media server available on port ${port}`);
        }
    }

    /**
     * Store a media file and return its public URL
     */
    async storeFile(fileBuffer: Buffer, originalName: string, mimeType: string): Promise<string> {
        console.log('=== storeFile called ===');
        console.log('File:', originalName, 'Size:', fileBuffer.length, 'MIME:', mimeType);
        console.log('useCloudStorage:', this.useCloudStorage);
        console.log('workerUrl:', this.workerUrl);
        
        try {
            // Try worker API first
            try {
                console.log('🔄 Attempting worker upload...');
                const result = await this.uploadToWorker(fileBuffer, originalName, mimeType);
                console.log('✅ Worker upload successful:', result);
                return result;
            } catch (workerError) {
                console.warn('❌ Worker upload failed:', workerError);
                
                if (this.useCloudStorage) {
                    // In production, don't fall back to local storage - throw the error
                    console.error('🚨 Production worker upload failed, no fallback available');
                    throw new Error(`Failed to upload to cloud storage: ${workerError instanceof Error ? workerError.message : 'Unknown error'}`);
                } else {
                    // Only fall back to local storage in development
                    console.log('⚠️ Development mode: falling back to local storage');
                    const ext = path.extname(originalName);
                    const filename = `${uuidv4()}${ext}`;
                    const filePath = path.join(this.storageDir, filename);
                    
                    fs.writeFileSync(filePath, fileBuffer);
                    
                    // Return media server URL instead of file:// URL
                    const publicUrl = `http://localhost:${this.mediaServerPort}/media/${filename}`;
                    console.log(`📁 Stored file locally: ${originalName} -> ${publicUrl}`);
                    return publicUrl;
                }
            }
        } catch (error) {
            console.error('💥 Failed to store file:', error);
            throw error;
        }
    }

    /**
     * Upload file to worker API
     */
    private async uploadToWorker(fileBuffer: Buffer, originalName: string, mimeType: string): Promise<string> {
        const formData = new FormData();
        const blob = new Blob([fileBuffer], { type: mimeType });
        formData.append('file', blob);
        formData.append('filename', originalName);

        const response = await fetch(`${this.workerUrl}/api/upload`, {
            method: 'POST',
            body: formData,
        });

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Upload failed: ${response.status} ${errorText}`);
        }

        const result = await response.json();
        
        if (!result.success) {
            throw new Error(result.error || 'Upload failed');
        }

        console.log(`Stored file to worker API: ${originalName} -> ${result.url}`);
        return result.url;
    }

    /**
     * Get the local file path for a given filename
     */
    getLocalPath(filename: string): string {
        return path.join(this.storageDir, filename);
    }

    /**
     * Get all stored files
     */
    getStoredFiles(): string[] {
        try {
            return fs.readdirSync(this.storageDir);
        } catch {
            return [];
        }
    }

    /**
     * Delete a stored file
     */
    deleteFile(filename: string): boolean {
        try {
            const filePath = path.join(this.storageDir, filename);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
                return true;
            }
            return false;
        } catch {
            return false;
        }
    }

    /**
     * Get storage directory path
     */
    getStorageDir(): string {
        return this.storageDir;
    }
}

export const storageService = new StorageService();