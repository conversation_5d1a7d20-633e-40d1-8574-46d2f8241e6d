import { app, BrowserWindow, ipcMain, dialog } from 'electron';
import * as path from 'path';
import * as fs from 'fs';
import { EditorState, Scene } from '@shared/types';
import { storageService } from './storage';
import { mediaServer } from './media-server';
import WebSocket from 'ws';
import YTDlpWrap from 'yt-dlp-wrap';
import fetch from 'node-fetch';
import { createWriteStream } from 'fs';
import { pipeline } from 'stream/promises';
import { spawn } from 'child_process';

class EditorApp {
  private mainWindow: BrowserWindow | null = null;
  private webSocket: WebSocket | null = null;
  private webSocketUrl: string;
  private ytdlp: YTDlpWrap;
  private ytdlpReady: boolean = false;
  private ytdlpPath: string;
  private editorState: EditorState = {
    scenes: [],
    publishedScene: undefined,
    canvasSize: { width: 1920, height: 1080 }
  };

  constructor() {
    // Set environment variables to handle UTF-8 encoding on Windows
    process.env.PYTHONIOENCODING = 'utf-8';
    process.env.PYTHONUTF8 = '1';
    
    // Set WebSocket URL based on environment
    this.webSocketUrl = process.env.NODE_ENV === 'development' 
      ? 'ws://127.0.0.1:8787/websocket'  // Local development
      : 'wss://obs-browser-source.cloudflare-okay454.workers.dev/websocket'; // Production (hardcoded for packaged app)
    
    // Set up yt-dlp path
    this.ytdlpPath = path.join(app.getPath('userData'), 'yt-dlp', process.platform === 'win32' ? 'yt-dlp.exe' : 'yt-dlp');
    
    // Initialize yt-dlp
    this.ytdlp = new YTDlpWrap();
    
    console.log('=== OBS Browser Source Editor Starting ===');
    console.log('Environment:', process.env.NODE_ENV || 'production');
    console.log('Available env vars:', Object.keys(process.env).filter(key => key.includes('PRODUCTION') || key.includes('R2')));
    console.log('PRODUCTION_WEBSOCKET_URL:', process.env.PRODUCTION_WEBSOCKET_URL);
    console.log('WebSocket URL:', this.webSocketUrl);
    console.log('App packaged:', app.isPackaged);
    console.log('Process resourcesPath:', process.resourcesPath);
    console.log('__dirname:', __dirname);
    console.log('yt-dlp path:', this.ytdlpPath);
    
    this.setupEventHandlers();
    this.connectWebSocket();
    
    // Initialize yt-dlp asynchronously after app setup
    this.initializeYtDlp().catch(error => {
      console.error('Failed to initialize yt-dlp during startup:', error);
    });
  }

  private setupEventHandlers() {
    app.whenReady().then(async () => {
      // Start media server
      try {
        await mediaServer.start();
        // Update storage service with the actual port
        storageService.updateBaseUrl(mediaServer.getPort());
      } catch (error) {
        console.error('Failed to start media server:', error);
      }
      
      this.createWindow();

      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createWindow();
        }
      });
    });

    app.on('window-all-closed', async () => {
      // Stop media server when app is closing
      try {
        await mediaServer.stop();
      } catch (error) {
        console.error('Error stopping media server:', error);
      }
      
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // IPC handlers
    ipcMain.handle('get-editor-state', () => {
      console.log('get-editor-state called, returning:', {
        totalScenes: this.editorState.scenes.length,
        publishedScene: this.editorState.publishedScene ? { id: this.editorState.publishedScene.id, name: this.editorState.publishedScene.name } : null,
        activeSceneId: this.editorState.activeSceneId,
        scenes: this.editorState.scenes.map(s => ({ id: s.id, name: s.name, isDraft: s.isDraft }))
      });
      return this.editorState;
    });
    ipcMain.handle('update-scene', (_, scene: Scene) => this.updateScene(scene));
    ipcMain.handle('delete-scene', (_, sceneId: string) => this.deleteScene(sceneId));
    ipcMain.handle('set-active-scene', (_, sceneId: string) => this.setActiveScene(sceneId));
    ipcMain.handle('publish-scene', (_, sceneId: string) => this.publishScene(sceneId));
    ipcMain.handle('save-project', () => this.saveProject());
    ipcMain.handle('load-project', () => this.loadProject());
    ipcMain.handle('upload-media', (_, fileBuffer: Uint8Array, originalName: string, mimeType: string) => 
      this.uploadMedia(fileBuffer, originalName, mimeType));
    ipcMain.handle('set-websocket-url', (_, url: string) => this.setWebSocketUrl(url));
    ipcMain.handle('clear-all-scenes', () => this.clearAllScenes());
    ipcMain.handle('get-websocket-status', () => ({
      connected: this.webSocket?.readyState === WebSocket.OPEN,
      url: this.webSocketUrl
    }));
    ipcMain.handle('download-video', (_, url: string, options?: {outputPath?: string, useCookies?: boolean, extractAudio?: boolean, audioFormat?: string, format?: string, audioOnly?: boolean}) => this.downloadVideo(url, options));
    ipcMain.handle('get-ytdlp-status', () => ({ ready: this.ytdlpReady }));
    ipcMain.handle('extract-stream-url', (_, url: string, useCookies: boolean = false) => this.extractStreamUrl(url, useCookies));
  }

  private createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1400,
      height: 900,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        preload: path.join(__dirname, '../preload/index.js'),
      },
      title: 'OBS Browser Source Editor',
      icon: path.join(__dirname, '../../../assets/icon.png'), // Add icon later
    });

    if (process.env.NODE_ENV === 'development') {
      // Load dev server in development
      console.log('🔧 Loading development server');
      this.mainWindow.loadURL('http://localhost:5173');
      this.mainWindow.webContents.openDevTools();
    } else {
      // Load built files in production
      const rendererPath = app.isPackaged 
        ? path.join(process.resourcesPath, 'app.asar', 'out', 'renderer', 'index.html')
        : path.join(__dirname, '../renderer/index.html');
      
      console.log('📦 Loading packaged renderer from:', rendererPath);
      this.mainWindow.loadFile(rendererPath);
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    // Initialize editor state from saved data
    this.initializeEditorState();
  }

  private updateScene(scene: Scene) {
    const existingIndex = this.editorState.scenes.findIndex(s => s.id === scene.id);
    
    // Check if this scene is currently live
    const isCurrentlyLive = this.editorState.publishedScene?.id === scene.id;
    
    // Mark as draft when updating
    const draftScene = { ...scene, isDraft: true };
    
    if (existingIndex >= 0) {
      this.editorState.scenes[existingIndex] = draftScene;
    } else {
      this.editorState.scenes.push(draftScene);
    }

    // Don't broadcast draft changes to browser source
    if (isCurrentlyLive) {
      console.log(`Scene ${scene.name} updated (LIVE scene now has unpublished changes)`);
    } else {
      console.log(`Scene ${scene.name} updated as draft`);
    }
    
    return this.editorState;
  }

  private deleteScene(sceneId: string) {
    console.log('=== Deleting Scene ===');
    console.log('Scene ID to delete:', sceneId);
    console.log('Scenes before deletion:', this.editorState.scenes.map(s => ({ id: s.id, name: s.name })));
    console.log('Published scene before deletion:', this.editorState.publishedScene ? { id: this.editorState.publishedScene.id, name: this.editorState.publishedScene.name } : null);
    
    // Remove from both regular scenes and published scene
    this.editorState.scenes = this.editorState.scenes.filter(s => s.id !== sceneId);
    if (this.editorState.publishedScene?.id === sceneId) {
      this.editorState.publishedScene = undefined;
    }
    
    // If the deleted scene was the active scene, switch to another one
    if (this.editorState.activeSceneId === sceneId) {
      this.editorState.activeSceneId = this.editorState.scenes[0]?.id;
      console.log('Deleted scene was active, switched to:', this.editorState.activeSceneId);
    }
    
    console.log('Scenes after deletion:', this.editorState.scenes.map(s => ({ id: s.id, name: s.name })));
    console.log('Published scene after deletion:', this.editorState.publishedScene ? { id: this.editorState.publishedScene.id, name: this.editorState.publishedScene.name } : null);
    
    // Save state immediately to persist the deletion
    this.savePersistedState();
    
    return this.editorState;
  }

  private clearAllScenes() {
    console.log('=== Clearing All Scenes ===');
    console.log('Scenes before clear:', this.editorState.scenes.map(s => ({ id: s.id, name: s.name })));
    console.log('Published scene before clear:', this.editorState.publishedScene ? { id: this.editorState.publishedScene.id, name: this.editorState.publishedScene.name } : null);
    
    // Clear everything
    this.editorState.scenes = [];
    this.editorState.publishedScene = undefined;
    this.editorState.activeSceneId = undefined;
    
    console.log('All scenes cleared, saving immediately...');
    this.savePersistedState();
    
    return this.editorState;
  }

  private setActiveScene(sceneId: string) {
    this.editorState.activeSceneId = sceneId;
    
    // Only broadcast published scene to browser source
    const scene = this.editorState.publishedScene?.id === sceneId ? this.editorState.publishedScene : null;
    if (scene) {
      this.broadcastToWebSocket('set_active_scene', { sceneId });
      console.log(`Switched to published scene: ${scene.name}`);
    } else {
      // Scene is not published, so viewers won't see any change
      const draftScene = this.editorState.scenes.find(s => s.id === sceneId);
      console.log(`Switched to draft scene: ${draftScene?.name || 'Unknown'}. Viewers still see the last published scene.`);
    }
    
    return this.editorState;
  }

  private publishScene(sceneId: string) {
    console.log('=== Publishing Scene ===');
    console.log('Scene ID:', sceneId);
    
    const scene = this.editorState.scenes.find(s => s.id === sceneId);
    if (!scene) {
      console.log('❌ Scene not found');
      return { success: false, error: 'Scene not found' };
    }

    console.log('Scene to publish:', scene.name);
    console.log('Scene elements count:', scene.elements.length);

    // Create published version
    const publishedScene = {
      ...scene,
      isDraft: false,
      lastPublished: new Date()
    };

    // Mark ALL other scenes as drafts (only one scene can be live)
    this.editorState.scenes = this.editorState.scenes.map(s => 
      s.id === sceneId 
        ? publishedScene 
        : { ...s, isDraft: true }
    );

    // Set this as the published scene (only one can be live)
    this.editorState.publishedScene = publishedScene;

    console.log('✅ Published scene updated:', {
      publishedScene: this.editorState.publishedScene ? { id: this.editorState.publishedScene.id, name: this.editorState.publishedScene.name, isDraft: this.editorState.publishedScene.isDraft } : null
    });

    // Set this as the active scene since it's now live
    this.editorState.activeSceneId = sceneId;

    console.log('📡 About to broadcast to WebSocket...');

    // Broadcast published scene to browser source
    this.broadcastToWebSocket('publish_scene', { scene: publishedScene });
    
    // Save state to persist published scenes
    this.savePersistedState();
    
    console.log(`✅ Scene ${scene.name} published and is now LIVE. All other scenes marked as drafts.`);
    return { success: true, scene: publishedScene };
  }

  private async saveProject() {
    try {
      const result = await dialog.showSaveDialog(this.mainWindow!, {
        title: 'Save Project',
        defaultPath: 'project.obsconfig',
        filters: [
          { name: 'OBS Config', extensions: ['obsconfig'] },
          { name: 'JSON', extensions: ['json'] }
        ]
      });

      if (!result.canceled && result.filePath) {
        fs.writeFileSync(result.filePath, JSON.stringify(this.editorState, null, 2));
        return { success: true, path: result.filePath };
      }
      
      return { success: false };
    } catch (error) {
      console.error('Error saving project:', error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  private async loadProject() {
    try {
      const result = await dialog.showOpenDialog(this.mainWindow!, {
        title: 'Load Project',
        filters: [
          { name: 'OBS Config', extensions: ['obsconfig'] },
          { name: 'JSON', extensions: ['json'] }
        ],
        properties: ['openFile']
      });

      if (!result.canceled && result.filePaths[0]) {
        const data = fs.readFileSync(result.filePaths[0], 'utf8');
        this.editorState = JSON.parse(data);
        return { success: true, state: this.editorState };
      }
      
      return { success: false };
    } catch (error) {
      console.error('Error loading project:', error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  private async uploadMedia(fileBuffer: Uint8Array, originalName: string, mimeType: string) {
    try {
      const buffer = Buffer.from(fileBuffer);
      const publicUrl = await storageService.storeFile(buffer, originalName, mimeType);
      return { success: true, url: publicUrl };
    } catch (error) {
      console.error('Error uploading media:', error);
      return { success: false, error: error instanceof Error ? error.message : String(error) };
    }
  }

  private initializeEditorState() {
    console.log('=== Initializing Editor State ===');
    
    // Try to load persisted state
    const persistedState = this.loadPersistedState();
    if (persistedState) {
      console.log('Loaded persisted state:', {
        totalScenes: persistedState.scenes.length,
        publishedScene: persistedState.publishedScene ? { id: persistedState.publishedScene.id, name: persistedState.publishedScene.name } : null,
        activeSceneId: persistedState.activeSceneId
      });
      
      this.editorState = {
        ...this.editorState,
        ...persistedState,
        // Keep the default canvas size if not persisted
        canvasSize: persistedState.canvasSize || this.editorState.canvasSize
      };
    } else {
      console.log('No persisted state found, starting fresh');
    }

    // Auto-save state periodically
    setInterval(() => {
      this.savePersistedState();
    }, 5000); // Save every 5 seconds
  }

  private getStateFilePath(): string {
    return path.join(app.getPath('userData'), 'editor-state.json');
  }

  private loadPersistedState(): EditorState | null {
    try {
      const stateFilePath = this.getStateFilePath();
      console.log('🔍 Checking for persisted state at:', stateFilePath);
      
      if (fs.existsSync(stateFilePath)) {
        const stateData = fs.readFileSync(stateFilePath, 'utf8');
        console.log('📄 Raw persisted state file size:', stateData.length, 'bytes');
        
        const parsed = JSON.parse(stateData);
        console.log('📊 Parsed state preview:', {
          totalScenes: parsed.scenes?.length || 0,
          sceneIds: parsed.scenes?.map((s: any) => s.id) || [],
          publishedSceneId: parsed.publishedScene?.id || null,
          activeSceneId: parsed.activeSceneId || null
        });
        
        // Migrate from old array format to single scene format
        if (parsed.publishedScenes && Array.isArray(parsed.publishedScenes)) {
          // Take the first published scene (there should only be one anyway)
          parsed.publishedScene = parsed.publishedScenes[0] || undefined;
          delete parsed.publishedScenes; // Remove the old array
        }
        
        // Convert date strings back to Date objects
        if (parsed.publishedScene?.lastPublished) {
          parsed.publishedScene.lastPublished = new Date(parsed.publishedScene.lastPublished);
        }
        
        return parsed;
      }
    } catch (error) {
      console.error('Error loading persisted state:', error);
    }
    return null;
  }

  private savePersistedState() {
    try {
      const stateFilePath = this.getStateFilePath();
      const stateData = JSON.stringify(this.editorState, null, 2);
      fs.writeFileSync(stateFilePath, stateData, 'utf8');
      console.log('💾 Auto-saved state at', new Date().toISOString(), ':', {
        totalScenes: this.editorState.scenes.length,
        publishedScene: this.editorState.publishedScene ? { id: this.editorState.publishedScene.id, name: this.editorState.publishedScene.name } : null,
        activeSceneId: this.editorState.activeSceneId,
        scenes: this.editorState.scenes.map(s => ({ id: s.id, name: s.name }))
      });
    } catch (error) {
      console.error('Error saving persisted state:', error);
    }
  }

  private broadcastToWebSocket(type: string, data: any) {
    const message = { type, ...data };
    console.log('=== WebSocket Broadcast Attempt ===');
    console.log('Message:', message);
    console.log('WebSocket state:', this.webSocket?.readyState);
    console.log('WebSocket OPEN constant:', WebSocket.OPEN);
    
    if (this.webSocket && this.webSocket.readyState === WebSocket.OPEN) {
      this.webSocket.send(JSON.stringify(message));
      console.log('✅ Sent to WebSocket successfully');
    } else {
      console.log('❌ WebSocket not connected - Current state:', this.webSocket?.readyState);
      console.log('WebSocket URL:', this.webSocketUrl);
    }
  }

  private connectWebSocket() {
    console.log('=== WebSocket Connection Attempt ===');
    console.log('Connecting to:', this.webSocketUrl);
    
    try {
      this.webSocket = new WebSocket(this.webSocketUrl);

      this.webSocket.on('open', () => {
        console.log('✅ Connected to browser source WebSocket');
        console.log('WebSocket readyState:', this.webSocket?.readyState);
      });

      this.webSocket.on('message', (data) => {
        try {
          const message = JSON.parse(data.toString());
          console.log('📨 Received from WebSocket:', message);
          this.handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      });

      this.webSocket.on('close', (code, reason) => {
        console.log('❌ WebSocket connection closed');
        console.log('Close code:', code);
        console.log('Close reason:', reason?.toString());
        console.log('Will attempt to reconnect in 5 seconds...');
        setTimeout(() => this.connectWebSocket(), 5000);
      });

      this.webSocket.on('error', (error) => {
        console.error('❌ WebSocket error:', error);
        console.log('WebSocket URL that failed:', this.webSocketUrl);
      });
    } catch (error) {
      console.error('❌ Failed to create WebSocket connection:', error);
      console.log('Will retry in 5 seconds...');
      setTimeout(() => this.connectWebSocket(), 5000);
    }
  }

  private setWebSocketUrl(url: string) {
    this.webSocketUrl = url;
    if (this.webSocket) {
      this.webSocket.close();
    }
    this.connectWebSocket();
    return { success: true, url: this.webSocketUrl };
  }

  private async handleWebSocketMessage(message: any) {
    switch (message.type) {
      case 'refresh_stream_url_request':
        await this.handleStreamUrlRefreshRequest(message);
        break;
      default:
        console.log('Unhandled WebSocket message type:', message.type);
    }
  }

  private async handleStreamUrlRefreshRequest(message: any) {
    try {
      console.log('Handling stream URL refresh request:', message);
      
      const { elementId, originalUrl } = message;
      
      // Find the element in the published scene to get the useCookies setting
      let useCookies = false;
      if (this.editorState.publishedScene) {
        const element = this.editorState.publishedScene.elements.find(el => el.id === elementId);
        if (element && element.type === 'livestream') {
          // You might want to store useCookies as a property on the element
          // For now, default to true for better compatibility
          useCookies = true;
        }
      }
      
      // Extract new stream URL
      const result = await this.extractStreamUrl(originalUrl, useCookies);
      
      if (result.success) {
        console.log('Successfully refreshed stream URL for element:', elementId);
        
        // Update the element in the published scene
        if (this.editorState.publishedScene) {
          const element = this.editorState.publishedScene.elements.find(el => el.id === elementId);
          if (element && element.type === 'livestream') {
            element.streamUrl = result.streamUrl;
            element.lastUrlRefresh = new Date();
          }
        }
        
        // Send success response back to browser source
        this.broadcastToWebSocket('refresh_stream_url_response', {
          success: true,
          elementId,
          newStreamUrl: result.streamUrl
        });
      } else {
        console.error('Failed to refresh stream URL:', result.error);
        
        // Send error response back to browser source
        this.broadcastToWebSocket('refresh_stream_url_response', {
          success: false,
          elementId,
          error: result.error
        });
      }
    } catch (error) {
      console.error('Error in handleStreamUrlRefreshRequest:', error);
      
      // Send error response back to browser source
      this.broadcastToWebSocket('refresh_stream_url_response', {
        success: false,
        elementId: message.elementId,
        error: 'Internal error during stream URL refresh'
      });
    }
  }

  private async downloadYtDlpBinary(): Promise<boolean> {
    try {
      console.log('Downloading yt-dlp binary...');
      
      // Ensure directory exists
      const ytDlpDir = path.dirname(this.ytdlpPath);
      if (!fs.existsSync(ytDlpDir)) {
        fs.mkdirSync(ytDlpDir, { recursive: true });
      }
      
      // Determine download URL based on platform
      const platform = process.platform;
      let downloadUrl: string;
      
      if (platform === 'win32') {
        downloadUrl = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp.exe';
      } else if (platform === 'darwin') {
        downloadUrl = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp_macos';
      } else {
        downloadUrl = 'https://github.com/yt-dlp/yt-dlp/releases/latest/download/yt-dlp';
      }
      
      console.log('Downloading from:', downloadUrl);
      console.log('Saving to:', this.ytdlpPath);
      
      // Download the binary
      const response = await fetch(downloadUrl);
      if (!response.ok) {
        throw new Error(`Failed to download yt-dlp: ${response.statusText}`);
      }
      
      // Save to file
      if (response.body) {
        await pipeline(response.body, createWriteStream(this.ytdlpPath));
      } else {
        throw new Error('No response body received');
      }
      
      // Make executable on Unix-like systems
      if (platform !== 'win32') {
        fs.chmodSync(this.ytdlpPath, 0o755);
      }
      
      console.log('yt-dlp binary downloaded successfully');
      return true;
    } catch (error) {
      console.error('Failed to download yt-dlp binary:', error);
      return false;
    }
  }

  private async initializeYtDlp() {
    try {
      console.log('Initializing yt-dlp binary...');
      
      // Check if binary already exists
      if (fs.existsSync(this.ytdlpPath)) {
        console.log('yt-dlp binary already exists, checking if it works...');
        this.ytdlp = new YTDlpWrap(this.ytdlpPath);
        
        // Test if the binary works
        try {
          await this.ytdlp.exec(['--version']);
          console.log('Existing yt-dlp binary is working');
          this.ytdlpReady = true;
          return;
        } catch (error) {
          console.log('Existing binary is not working, will re-download...');
        }
      }
      
      // Download the binary
      const downloadSuccess = await this.downloadYtDlpBinary();
      
      if (downloadSuccess) {
        this.ytdlp = new YTDlpWrap(this.ytdlpPath);
        
        // Test the downloaded binary
        try {
          await this.ytdlp.exec(['--version']);
          console.log('Downloaded yt-dlp binary is working');
          this.ytdlpReady = true;
        } catch (error) {
          console.error('Downloaded binary is not working:', error);
          this.ytdlpReady = false;
        }
      } else {
        console.log('Falling back to system yt-dlp or yt-dlp-wrap auto-download...');
        this.ytdlp = new YTDlpWrap();
        
        try {
          // Try to use yt-dlp-wrap's built-in binary or auto-download
          await this.ytdlp.exec(['--version']);
          this.ytdlpReady = true;
          console.log('yt-dlp-wrap initialization successful');
        } catch (error) {
          console.error('All yt-dlp initialization methods failed:', error);
          this.ytdlpReady = false;
        }
      }
    } catch (error) {
      console.error('Failed to initialize yt-dlp:', error);
      this.ytdlpReady = false;
    }
  }

  private findFirefoxCookies(profilesPath: string): string | null {
    try {
      if (!fs.existsSync(profilesPath)) {
        return null;
      }
      
      const profiles = fs.readdirSync(profilesPath, { withFileTypes: true });
      for (const profile of profiles) {
        if (profile.isDirectory() && profile.name.includes('default')) {
          const cookiesPath = path.join(profilesPath, profile.name, 'cookies.sqlite');
          if (fs.existsSync(cookiesPath)) {
            return cookiesPath;
          }
        }
      }
      
      // Fallback: look for any profile with cookies
      for (const profile of profiles) {
        if (profile.isDirectory()) {
          const cookiesPath = path.join(profilesPath, profile.name, 'cookies.sqlite');
          if (fs.existsSync(cookiesPath)) {
            return cookiesPath;
          }
        }
      }
    } catch (error) {
      console.log('Error finding Firefox cookies:', error);
    }
    return null;
  }

  private getBrowserCookiePaths() {
    const os = require('os');
    const platform = process.platform;
    const userHome = os.homedir();
    
    console.log(`Platform: ${platform}, User home: ${userHome}`);
    
    const browsers = [];
    
    if (platform === 'win32') {
      // Windows paths
      browsers.push(
        { name: 'Chrome', path: path.join(userHome, 'AppData', 'Local', 'Google', 'Chrome', 'User Data', 'Default', 'Cookies') },
        { name: 'Edge', path: path.join(userHome, 'AppData', 'Local', 'Microsoft', 'Edge', 'User Data', 'Default', 'Cookies') }
      );
      
      // Handle Firefox separately with proper profile detection
      const firefoxProfilesPath = path.join(userHome, 'AppData', 'Roaming', 'Mozilla', 'Firefox', 'Profiles');
      const firefoxCookies = this.findFirefoxCookies(firefoxProfilesPath);
      if (firefoxCookies) {
        browsers.push({ name: 'Firefox', path: firefoxCookies });
      }
    } else if (platform === 'darwin') {
      // macOS paths
      browsers.push(
        { name: 'Chrome', path: path.join(userHome, 'Library', 'Application Support', 'Google', 'Chrome', 'Default', 'Cookies') },
        { name: 'Safari', path: path.join(userHome, 'Library', 'Cookies', 'Cookies.binarycookies') }
      );
      
      const firefoxProfilesPath = path.join(userHome, 'Library', 'Application Support', 'Firefox', 'Profiles');
      const firefoxCookies = this.findFirefoxCookies(firefoxProfilesPath);
      if (firefoxCookies) {
        browsers.push({ name: 'Firefox', path: firefoxCookies });
      }
    } else {
      // Linux paths
      browsers.push(
        { name: 'Chrome', path: path.join(userHome, '.config', 'google-chrome', 'Default', 'Cookies') }
      );
      
      const firefoxProfilesPath = path.join(userHome, '.mozilla', 'firefox');
      const firefoxCookies = this.findFirefoxCookies(firefoxProfilesPath);
      if (firefoxCookies) {
        browsers.push({ name: 'Firefox', path: firefoxCookies });
      }
    }
    
    console.log('Browser paths generated:', browsers);
    return browsers;
  }

  // Custom yt-dlp execution that returns stdout for URL extraction
  private async execYtDlpWithEncodingForUrl(args: string[]): Promise<string> {
    return new Promise((resolve, reject) => {
      console.log('Executing yt-dlp with encoding handling for URL:', args.join(' '));
      
      const ytdlpBinary = fs.existsSync(this.ytdlpPath) ? this.ytdlpPath : 'yt-dlp';
      
      const child = spawn(ytdlpBinary, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
          PYTHONUTF8: '1',
          PYTHONLEGACYWINDOWSSTDIO: '1'
        },
        shell: false
      });

      let stdout = '';
      let stderr = '';

      // Handle stdout with encoding error resilience
      child.stdout?.on('data', (data) => {
        try {
          stdout += data.toString('utf-8');
        } catch (error) {
          stdout += data.toString('latin1');
        }
      });

      // Handle stderr with encoding error resilience
      child.stderr?.on('data', (data) => {
        try {
          stderr += data.toString('utf-8');
        } catch (error) {
          stderr += data.toString('latin1');
        }
      });

      child.on('close', (code) => {
        console.log('yt-dlp process finished with code:', code);
        console.log('stdout:', stdout);
        if (stderr) {
          console.log('stderr:', stderr);
        }

        if (code === 0) {
          resolve(stdout.trim());
        } else {
          reject(new Error(`yt-dlp failed with code ${code}: ${stderr || stdout}`));
        }
      });

      child.on('error', (error) => {
        console.error('yt-dlp process error:', error);
        reject(error);
      });
    });
  }

  // Custom yt-dlp execution with proper encoding handling
  private async execYtDlpWithEncoding(args: string[]): Promise<void> {
    return new Promise((resolve, reject) => {
      console.log('Executing yt-dlp with encoding handling:', args.join(' '));
      
      const ytdlpBinary = fs.existsSync(this.ytdlpPath) ? this.ytdlpPath : 'yt-dlp';
      
      const child = spawn(ytdlpBinary, args, {
        stdio: ['ignore', 'pipe', 'pipe'],
        env: {
          ...process.env,
          PYTHONIOENCODING: 'utf-8',
          PYTHONUTF8: '1',
          // Force console encoding on Windows
          PYTHONLEGACYWINDOWSSTDIO: '1'
        },
        shell: false
      });

      let stdout = '';
      let stderr = '';

      // Handle stdout with encoding error resilience
      child.stdout?.on('data', (data) => {
        try {
          // Try UTF-8 first
          stdout += data.toString('utf-8');
        } catch (error) {
          // Fallback to latin1 if UTF-8 fails
          stdout += data.toString('latin1');
        }
      });

      // Handle stderr with encoding error resilience
      child.stderr?.on('data', (data) => {
        try {
          // Try UTF-8 first
          stderr += data.toString('utf-8');
        } catch (error) {
          // Fallback to latin1 if UTF-8 fails
          stderr += data.toString('latin1');
        }
      });

      child.on('close', (code) => {
        console.log('yt-dlp process finished with code:', code);
        console.log('stdout:', stdout);
        if (stderr) {
          console.log('stderr:', stderr);
        }

        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`yt-dlp failed with code ${code}: ${stderr || stdout}`));
        }
      });

      child.on('error', (error) => {
        console.error('yt-dlp process error:', error);
        reject(error);
      });
    });
  }

  private async downloadVideo(url: string, options?: {
    outputPath?: string, 
    useCookies?: boolean,
    extractAudio?: boolean,
    audioFormat?: string,
    format?: string,
    audioOnly?: boolean
  }) {
    try {
      console.log('Starting video download:', url);
      console.log('Download options:', options);
      
      // Check if ytdlp is properly initialized
      if (!this.ytdlpReady) {
        console.log('yt-dlp not ready, attempting to initialize...');
        await this.initializeYtDlp();
        
        if (!this.ytdlpReady) {
          throw new Error('yt-dlp is not available. Please ensure you have an internet connection and restart the application.');
        }
      }
      
      // Get video info first with retry logic
      let info;
      try {
        info = await this.ytdlp.getVideoInfo(url);
        console.log('Video info retrieved:', info.title);
      } catch (infoError) {
        console.log('Failed to get video info, trying direct download...');
        // If getting info fails, we'll still try to download with a generic filename
        info = { title: 'downloaded_video' };
      }
      
      // Determine output path
      const os = require('os');
      const userHome = os.homedir();
      const downloadsPath = (options?.outputPath && typeof options.outputPath === 'string') 
        ? options.outputPath 
        : path.join(userHome, 'Downloads');
      
      console.log(`Downloads path: ${downloadsPath} (type: ${typeof downloadsPath})`);
      
      // Ensure downloads directory exists
      if (typeof downloadsPath === 'string' && !fs.existsSync(downloadsPath)) {
        fs.mkdirSync(downloadsPath, { recursive: true });
      }
      
      // Use a simple, safe filename approach
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const baseFilename = `video_${timestamp}`;
      
      // Set output directory only, let yt-dlp handle the filename
      const outputTemplate = path.join(downloadsPath, '%(title)s.%(ext)s');
      console.log(`Output template: ${outputTemplate}`);
      
      console.log('Downloading to:', outputTemplate);
      
      // Build yt-dlp command arguments
      const args = [
        url,
        '-o', outputTemplate,
        '--no-playlist',
        '--restrict-filenames', // Make filenames safe for Windows
        '--windows-filenames',  // Use Windows-safe filenames
        '--encoding', 'utf-8',  // Force UTF-8 encoding
        '--ignore-errors',      // Continue on errors
        '--no-warnings'         // Reduce verbose output
      ];

      // Handle format selection based on download type
      if (options?.extractAudio || options?.audioOnly) {
        // Audio download configuration
        args.push('--extract-audio');
        if (options.audioFormat) {
          args.push('--audio-format', options.audioFormat);
        }
        // Use best audio format available
        args.push('--format', options.format || 'bestaudio/best');
      } else {
        // Video download configuration
        args.push('--format', options?.format || 'best[height<=1080]/best');
      }
      
      // Add cookies support if requested
      if (options?.useCookies) {
        console.log('Using Firefox browser cookies for authentication...');
        // Always use Firefox cookies as they don't have DPAPI encryption issues
        args.push('--cookies-from-browser', 'firefox');
        
        // Add additional options to avoid bot detection
        args.push(
          '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
          '--sleep-interval', '1',
          '--max-sleep-interval', '5'
        );
      } else {
        // Even without cookies, add some bot-avoidance measures
        args.push(
          '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
          '--sleep-interval', '2'
        );
      }
      
      console.log('yt-dlp command:', args.join(' '));
      
      // Execute with proper encoding handling for Windows
      try {
        await this.execYtDlpWithEncoding(args);
      } catch (execError) {
        // If it's a UTF-8 encoding error, try with simpler arguments
        if (execError instanceof Error && (
          execError.message.includes('utf-8') || 
          execError.message.includes('codec') ||
          execError.message.includes('decode') ||
          execError.message.includes('encoding')
        )) {
          console.log('Encoding error detected, retrying with basic options...');
          // Retry with minimal arguments to avoid encoding issues
          const basicArgs = [
            url,
            '-o', path.join(downloadsPath, 'video_%(epoch)s.%(ext)s'),
            '--no-playlist',
            '--restrict-filenames'
          ];
          
          // Add format based on download type for retry
          if (options?.extractAudio || options?.audioOnly) {
            basicArgs.push('--extract-audio');
            if (options.audioFormat) {
              basicArgs.push('--audio-format', options.audioFormat);
            }
            basicArgs.push('--format', 'bestaudio/best');
          } else {
            basicArgs.push('--format', 'best[height<=720]/best');
          }
          console.log('Retrying with basic command:', basicArgs.join(' '));
          await this.execYtDlpWithEncoding(basicArgs);
        } else if (execError instanceof Error && (
          execError.message.includes('Sign in to confirm') || 
          execError.message.includes('not a bot') ||
          execError.message.includes('authentication') ||
          execError.message.includes('Extracted 0 cookies')
        )) {
          console.log('Authentication error detected, trying without cookies...');
          
          // Try fallback without cookies but with Firefox user agent
          const fallbackArgs = [
            url,
            '-o', path.join(downloadsPath, 'video_%(epoch)s.%(ext)s'),
            '--format', 'best[height<=720]/best',
            '--no-playlist',
            '--restrict-filenames',
            '--user-agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
            '--sleep-interval', '3',
            '--max-sleep-interval', '8',
            '--extractor-retries', '3'
          ];
          
          console.log('Retrying without cookies:', fallbackArgs.join(' '));
          await this.execYtDlpWithEncoding(fallbackArgs);
        } else {
          throw execError;
        }
      }
      
      console.log('Video downloaded successfully');
      
      return {
        success: true,
        title: info.title,
        outputPath: downloadsPath,
        message: `Downloaded "${info.title || 'video'}" to Downloads folder`
      };
    } catch (error) {
      console.error('Error downloading video:', error);
      
      // Provide more specific error messages
      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        if (error.message.includes('ENOENT')) {
          errorMessage = 'yt-dlp binary not found. Please restart the application to download it.';
        } else if (error.message.includes('Unsupported URL')) {
          errorMessage = 'Unsupported URL. Please check if the URL is valid and supported by yt-dlp.';
        } else if (error.message.includes('Sign in to confirm') || error.message.includes('not a bot')) {
          errorMessage = 'YouTube requires authentication to download this video. Please:\n1. Install Firefox and sign into YouTube\n2. Enable cookies in the download options\n3. Try a different video - some videos have restrictions';
        } else if (error.message.includes('Extracted 0 cookies')) {
          errorMessage = 'No Firefox cookies found. Please:\n1. Install Firefox if you haven\'t already\n2. Sign into YouTube in Firefox\n3. Try downloading again with cookies enabled';
        } else if (error.message.includes('Video unavailable')) {
          errorMessage = 'Video is unavailable. It may be private, deleted, or region-locked.';
        } else if (error.message.includes('Requested format is not available')) {
          errorMessage = 'Requested video format is not available. Try downloading with lower quality settings.';
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }

  private async extractStreamUrl(url: string, useCookies: boolean = false) {
    try {
      console.log('Extracting stream URL for:', url);
      
      // Check if ytdlp is properly initialized
      if (!this.ytdlpReady) {
        console.log('yt-dlp not ready, attempting to initialize...');
        await this.initializeYtDlp();
        
        if (!this.ytdlpReady) {
          throw new Error('yt-dlp is not available. Please ensure you have an internet connection and restart the application.');
        }
      }

      // Detect platform from URL
      let platform = 'other';
      if (url.includes('youtube.com') || url.includes('youtu.be')) {
        platform = 'youtube';
      } else if (url.includes('twitch.tv')) {
        platform = 'twitch';
      } else if (url.includes('kick.com')) {
        platform = 'kick';
      }

      // Build yt-dlp command arguments for URL extraction
      const args = [
        url,
        '--get-url',
        '--no-playlist'
      ];

      // Add cookies support if requested
      if (useCookies) {
        if (platform === 'youtube') {
          args.push('--cookies-from-browser', 'firefox');
        }
      }

      console.log('yt-dlp extract command:', args.join(' '));
      
      // Execute yt-dlp to get stream URL
      let streamUrl = '';
      let title = '';
      
      try {
        // Use our custom execution method that properly captures stdout
        console.log('Executing yt-dlp with args:', args);
        streamUrl = await this.execYtDlpWithEncodingForUrl(args);
        
        if (!streamUrl || streamUrl.trim() === '') {
          throw new Error('No stream URL returned from yt-dlp');
        }
        
        // Also try to get title
        try {
          const info = await this.ytdlp.getVideoInfo(url);
          title = info.title;
        } catch (infoError) {
          console.log('Could not get video info, using URL as title');
          title = url;
        }
        
        console.log('Successfully extracted stream URL:', streamUrl);
        
        return {
          success: true,
          streamUrl,
          platform,
          title,
          message: `Stream URL extracted for ${platform}`
        };
      } catch (execError) {
        console.error('Failed to extract stream URL:', execError);
        throw execError;
      }
      
    } catch (error) {
      console.error('Error extracting stream URL:', error);
      
      let errorMessage = 'Unknown error occurred';
      if (error instanceof Error) {
        if (error.message.includes('not currently live')) {
          errorMessage = 'This stream is not currently live. Please check the URL and try again.';
        } else if (error.message.includes('Sign in to confirm') || error.message.includes('not a bot')) {
          errorMessage = 'YouTube requires authentication. Please:\n1. Install Firefox and sign into YouTube\n2. Try again';
        } else if (error.message.includes('Unsupported URL')) {
          errorMessage = 'Unsupported URL. Please check if the URL is valid and supported by yt-dlp.';
        } else {
          errorMessage = error.message;
        }
      }
      
      return {
        success: false,
        error: errorMessage
      };
    }
  }
}

new EditorApp();
