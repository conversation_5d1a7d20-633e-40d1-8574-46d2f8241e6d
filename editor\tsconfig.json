{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020", "DOM"], "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "jsx": "react-jsx", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@shared/*": ["../shared/*"], "@/*": ["src/renderer/*"]}, "types": ["node"]}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist-electron"]}