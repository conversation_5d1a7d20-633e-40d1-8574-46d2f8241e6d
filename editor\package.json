{"name": "obs-browser-source-editor", "version": "1.0.0", "description": "Desktop editor for OBS browser sources", "main": "out/main/index.js", "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "package": "electron-builder"}, "keywords": ["obs", "streaming", "electron"], "author": "You", "license": "MIT", "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^8.0.0", "electron": "^27.0.0", "electron-builder": "^26.0.0", "electron-vite": "^4.0.0", "postcss": "^8.5.6", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "vite": "^7.0.5"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/uuid": "^9.0.0", "@types/ws": "^8.18.1", "bufferutil": "^4.0.9", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "hls.js": "^1.6.7", "lucide-react": "^0.525.0", "node-fetch": "^3.3.2", "react": "^18.0.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.0.0", "react-rnd": "^10.5.2", "utf-8-validate": "^6.0.5", "uuid": "^9.0.0", "ws": "^8.18.3", "yt-dlp-wrap": "^2.3.12"}, "build": {"appId": "com.yourcompany.obs-browser-source-editor", "productName": "OBS Browser Source Editor", "directories": {"output": "dist"}, "files": ["out/**/*", "package.json"], "extraFiles": [], "win": {"target": "nsis"}}}