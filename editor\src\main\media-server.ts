import express from 'express';
import * as path from 'path';
import cors from 'cors';
import { storageService } from './storage';

export class MediaServer {
    private app: express.Express;
    private server: any;
    private port: number = 3001;

    constructor() {
        this.app = express();
        this.setupMiddleware();
        this.setupRoutes();
    }

    private setupMiddleware() {
        // Enable CORS for all origins
        this.app.use(cors());
    }

    private setupRoutes() {
        // Serve media files from storage directory
        this.app.use('/media', express.static(storageService.getStorageDir(), {
            setHeaders: (res, filePath) => {
                // Set appropriate MIME type based on file extension
                const ext = path.extname(filePath).toLowerCase();
                if (['.jpg', '.jpeg', '.png', '.gif', '.webp'].includes(ext)) {
                    res.set('Content-Type', `image/${ext.slice(1)}`);
                } else if (['.mp4', '.webm', '.mov', '.avi'].includes(ext)) {
                    res.set('Content-Type', `video/${ext.slice(1)}`);
                } else if (['.mp3', '.wav', '.ogg', '.m4a', '.aac'].includes(ext)) {
                    res.set('Content-Type', `audio/${ext.slice(1)}`);
                }
                
                // Allow cross-origin requests
                res.set('Access-Control-Allow-Origin', '*');
            }
        }));

        // Handle OPTIONS requests for CORS
        this.app.options('/stream/:streamId', (req, res) => {
            res.set('Access-Control-Allow-Origin', '*');
            res.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
            res.set('Access-Control-Allow-Headers', 'Range, Content-Type');
            res.status(200).end();
        });

        // Proxy livestream endpoint
        this.app.get('/stream/:streamId', async (req, res) => {
            try {
                const streamUrl = decodeURIComponent(req.params.streamId);
                console.log('Proxying stream:', streamUrl);
                
                // Set CORS headers first
                res.set('Access-Control-Allow-Origin', '*');
                res.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
                res.set('Access-Control-Allow-Headers', 'Range, Content-Type, Accept-Encoding');
                
                // Handle range requests for video streaming
                const range = req.headers.range;
                
                // Forward the request to the original stream
                const fetch = (await import('node-fetch')).default;
                const response = await fetch(streamUrl, {
                    headers: {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0) Gecko/20100101 Firefox/91.0',
                        'Accept': 'application/vnd.apple.mpegurl, application/x-mpegurl, application/octet-stream, */*',
                        'Accept-Encoding': 'identity', // Prevent compression issues
                        ...(range && { 'Range': range })
                    }
                });
                
                console.log('Response status:', response.status);
                console.log('Response headers:', Object.fromEntries(response.headers.entries()));
                
                // Set status code first
                res.status(response.status);
                
                // Copy essential headers from the original response
                const contentType = response.headers.get('content-type');
                if (contentType) {
                    res.set('Content-Type', contentType);
                } else {
                    // Default to HLS content type
                    res.set('Content-Type', 'application/vnd.apple.mpegurl');
                }
                
                // Copy other important headers
                ['content-length', 'cache-control', 'expires', 'last-modified', 'etag'].forEach(header => {
                    const value = response.headers.get(header);
                    if (value) {
                        res.set(header, value);
                    }
                });
                
                // Don't set content-encoding to avoid decoding issues
                res.removeHeader('content-encoding');
                
                // Get the response as text for HLS manifests
                if (contentType && contentType.includes('mpegurl')) {
                    const text = await response.text();
                    console.log('HLS manifest content:', text.substring(0, 200) + '...');
                    res.send(text);
                } else {
                    // For other content, pipe the response
                    if (response.body) {
                        response.body.pipe(res);
                    } else {
                        res.end();
                    }
                }
                
            } catch (error) {
                console.error('Stream proxy error:', error);
                res.status(500).json({ error: 'Failed to proxy stream' });
            }
        });

        // Health check endpoint
        this.app.get('/health', (req, res) => {
            res.json({ 
                status: 'ok', 
                storageDir: storageService.getStorageDir(),
                port: this.port
            });
        });
    }

    public start(): Promise<void> {
        return new Promise((resolve, reject) => {
            const tryPort = (port: number) => {
                this.server = this.app.listen(port, (err?: Error) => {
                    if (err) {
                        if ((err as any).code === 'EADDRINUSE' && port < 3010) {
                            console.log(`Port ${port} in use, trying ${port + 1}...`);
                            tryPort(port + 1);
                        } else {
                            console.error('Failed to start media server:', err);
                            reject(err);
                        }
                    } else {
                        this.port = port;
                        console.log(`Media server running on http://localhost:${this.port}`);
                        console.log(`Serving files from: ${storageService.getStorageDir()}`);
                        resolve();
                    }
                });
            };
            
            tryPort(this.port);
        });
    }

    public stop(): Promise<void> {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('Media server stopped');
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }

    public getPort(): number {
        return this.port;
    }
}

export const mediaServer = new MediaServer();