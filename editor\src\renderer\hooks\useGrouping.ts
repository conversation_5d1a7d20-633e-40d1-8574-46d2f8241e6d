import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { ElementGroup, Scene, EditorState } from '@shared/types';

interface UseGroupingProps {
    activeScene: Scene | null | undefined;
    editorState: EditorState;
    saveToHistory: (state: EditorState) => void;
    updateScene: (scene: Scene) => void;
}

export const useGrouping = ({
    activeScene,
    editorState,
    saveToHistory,
    updateScene
}: UseGroupingProps) => {

    const createGroup = useCallback((elementIds: string[], groupName?: string) => {
        if (!activeScene || elementIds.length === 0) return;
        
        saveToHistory(editorState);
        
        const newGroup: ElementGroup = {
            id: uuidv4(),
            name: groupName || `Group ${(activeScene.groups?.length || 0) + 1}`,
            elementIds: [...elementIds],
            visible: true,
            locked: false,
            collapsed: false
        };
        
        const updatedScene = {
            ...activeScene,
            groups: [...(activeScene.groups || []), newGroup]
        };
        
        updateScene(updatedScene);
        return newGroup;
    }, [activeScene, editorState, saveToHistory, updateScene]);

    const ungroupElements = useCallback((groupId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const updatedScene = {
            ...activeScene,
            groups: (activeScene.groups || []).filter(g => g.id !== groupId)
        };
        
        updateScene(updatedScene);
    }, [activeScene, editorState, saveToHistory, updateScene]);

    const addToGroup = useCallback((groupId: string, elementIds: string[]) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const updatedGroups = (activeScene.groups || []).map(group => 
            group.id === groupId 
                ? { ...group, elementIds: [...new Set([...group.elementIds, ...elementIds])] }
                : group
        );
        
        const updatedScene = {
            ...activeScene,
            groups: updatedGroups
        };
        
        updateScene(updatedScene);
    }, [activeScene, editorState, saveToHistory, updateScene]);

    const removeFromGroup = useCallback((groupId: string, elementIds: string[]) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const updatedGroups = (activeScene.groups || []).map(group => 
            group.id === groupId 
                ? { ...group, elementIds: group.elementIds.filter(id => !elementIds.includes(id)) }
                : group
        ).filter(group => group.elementIds.length > 0); // Remove empty groups
        
        const updatedScene = {
            ...activeScene,
            groups: updatedGroups
        };
        
        updateScene(updatedScene);
    }, [activeScene, editorState, saveToHistory, updateScene]);

    const toggleGroupVisibility = useCallback((groupId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const group = (activeScene.groups || []).find(g => g.id === groupId);
        if (!group) return;
        
        const newVisibility = !group.visible;
        
        // Update group visibility
        const updatedGroups = (activeScene.groups || []).map(g => 
            g.id === groupId ? { ...g, visible: newVisibility } : g
        );
        
        // Update elements in group
        const updatedElements = activeScene.elements.map(el => 
            group.elementIds.includes(el.id) 
                ? { ...el, visible: newVisibility }
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            groups: updatedGroups,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
    }, [activeScene, editorState, saveToHistory, updateScene]);

    const toggleGroupLock = useCallback((groupId: string) => {
        if (!activeScene) return;
        
        const updatedGroups = (activeScene.groups || []).map(group => 
            group.id === groupId 
                ? { ...group, locked: !group.locked }
                : group
        );
        
        const updatedScene = {
            ...activeScene,
            groups: updatedGroups
        };
        
        updateScene(updatedScene);
    }, [activeScene, updateScene]);

    const renameGroup = useCallback((groupId: string, newName: string) => {
        if (!activeScene) return;
        
        const updatedGroups = (activeScene.groups || []).map(group => 
            group.id === groupId 
                ? { ...group, name: newName }
                : group
        );
        
        const updatedScene = {
            ...activeScene,
            groups: updatedGroups
        };
        
        updateScene(updatedScene);
    }, [activeScene, updateScene]);

    return {
        createGroup,
        ungroupElements,
        addToGroup,
        removeFromGroup,
        toggleGroupVisibility,
        toggleGroupLock,
        renameGroup
    };
};
