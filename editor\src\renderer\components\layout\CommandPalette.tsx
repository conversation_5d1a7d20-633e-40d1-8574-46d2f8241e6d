import React, { useState, useEffect, useMemo } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { 
    Search, 
    Type, 
    Image, 
    Video, 
    Volume2, 
    Radio, 
    Plus, 
    Trash2, 
    Copy, 
    Clipboard,
    Save,
    FolderOpen,
    Send,
    Maximize,
    Eye,
    EyeOff,
    ArrowUp,
    ArrowDown,
    Layers,
    Download
} from 'lucide-react';
import type { Scene, Element, EditorState } from '@shared/types';

interface Command {
    id: string;
    label: string;
    description: string;
    icon: React.ComponentType<any>;
    action: () => void;
    category: 'scene' | 'element' | 'edit' | 'file' | 'view';
    keywords?: string[];
    disabled?: boolean;
}

interface CommandPaletteProps {
    isOpen: boolean;
    onClose: () => void;
    editorState: EditorState;
    activeScene: Scene | undefined;
    selectedElement: Element | null;
    
    // Actions
    selectScene: (sceneId: string) => void;
    createNewScene: () => void;
    deleteScene: (sceneId: string) => void;
    addTextElement: () => void;
    addImageElement: () => void;
    addVideoElement: () => void;
    addAudioElement: () => void;
    addLiveStreamElement: () => void;
    deleteElement: (elementId: string) => void;
    onSave: () => void;
    onLoad: () => void;
    onCopy: () => void;
    onPaste: () => void;
    onDuplicate: () => void;
    onPublish: () => void;
    onCenter: () => void;
    onZoomToFit?: () => void;
    onOpenDownloader?: () => void;
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({
    isOpen,
    onClose,
    editorState,
    activeScene,
    selectedElement,
    selectScene,
    createNewScene,
    deleteScene,
    addTextElement,
    addImageElement,
    addVideoElement,
    addAudioElement,
    addLiveStreamElement,
    deleteElement,
    onSave,
    onLoad,
    onCopy,
    onPaste,
    onDuplicate,
    onPublish,
    onCenter,
    onZoomToFit,
    onOpenDownloader
}) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedIndex, setSelectedIndex] = useState(0);

    // Generate all available commands
    const commands: Command[] = useMemo(() => {
        const cmds: Command[] = [];

        // File commands
        cmds.push({
            id: 'file.save',
            label: 'Save Project',
            description: 'Save current project to file',
            icon: Save,
            action: () => { onSave(); onClose(); },
            category: 'file',
            keywords: ['save', 'file', 'export']
        });

        cmds.push({
            id: 'file.load',
            label: 'Load Project',
            description: 'Open project from file',
            icon: FolderOpen,
            action: () => { onLoad(); onClose(); },
            category: 'file',
            keywords: ['load', 'open', 'import']
        });

        // Edit commands

        if (selectedElement) {
            cmds.push({
                id: 'edit.copy',
                label: 'Copy Element',
                description: `Copy ${selectedElement.name}`,
                icon: Copy,
                action: () => { onCopy(); onClose(); },
                category: 'edit',
                keywords: ['copy', 'duplicate']
            });

            cmds.push({
                id: 'edit.duplicate',
                label: 'Duplicate Element',
                description: `Duplicate ${selectedElement.name}`,
                icon: Copy,
                action: () => { onDuplicate(); onClose(); },
                category: 'edit',
                keywords: ['duplicate', 'clone']
            });

            cmds.push({
                id: 'edit.delete',
                label: 'Delete Element',
                description: `Delete ${selectedElement.name}`,
                icon: Trash2,
                action: () => { deleteElement(selectedElement.id); onClose(); },
                category: 'edit',
                keywords: ['delete', 'remove', 'trash']
            });
        }

        cmds.push({
            id: 'edit.paste',
            label: 'Paste Element',
            description: 'Paste copied element',
            icon: Clipboard,
            action: () => { onPaste(); onClose(); },
            category: 'edit',
            keywords: ['paste', 'insert']
        });

        // Scene commands
        cmds.push({
            id: 'scene.new',
            label: 'New Scene',
            description: 'Create a new scene',
            icon: Plus,
            action: () => { createNewScene(); onClose(); },
            category: 'scene',
            keywords: ['new', 'scene', 'create', 'add']
        });

        editorState.scenes.forEach(scene => {
            cmds.push({
                id: `scene.select.${scene.id}`,
                label: `Go to ${scene.name}`,
                description: `Switch to scene: ${scene.name}`,
                icon: Layers,
                action: () => { selectScene(scene.id); onClose(); },
                category: 'scene',
                keywords: ['scene', 'switch', 'go', scene.name.toLowerCase()]
            });
        });

        if (activeScene) {
            cmds.push({
                id: 'scene.delete',
                label: 'Delete Scene',
                description: `Delete scene: ${activeScene.name}`,
                icon: Trash2,
                action: () => { deleteScene(activeScene.id); onClose(); },
                category: 'scene',
                keywords: ['delete', 'remove', 'scene']
            });

            if (activeScene.isDraft) {
                cmds.push({
                    id: 'scene.publish',
                    label: 'Publish Scene',
                    description: `Publish scene: ${activeScene.name}`,
                    icon: Send,
                    action: () => { onPublish(); onClose(); },
                    category: 'scene',
                    keywords: ['publish', 'go live', 'broadcast']
                });
            }
        }

        // Element creation commands
        cmds.push({
            id: 'element.add.text',
            label: 'Add Text Element',
            description: 'Add a new text element',
            icon: Type,
            action: () => { addTextElement(); onClose(); },
            category: 'element',
            keywords: ['add', 'text', 'label', 'title']
        });

        cmds.push({
            id: 'element.add.image',
            label: 'Add Image Element',
            description: 'Add a new image element',
            icon: Image,
            action: () => { addImageElement(); onClose(); },
            category: 'element',
            keywords: ['add', 'image', 'picture', 'photo']
        });

        cmds.push({
            id: 'element.add.video.upload',
            label: 'Add Video (Upload)',
            description: 'Upload and add a video file',
            icon: Video,
            action: () => { addVideoElement(); onClose(); },
            category: 'element',
            keywords: ['add', 'video', 'upload', 'file', 'movie', 'clip']
        });

        if (onOpenDownloader) {
            cmds.push({
                id: 'element.add.video.download',
                label: 'Add Video (Download)',
                description: 'Download video from URL',
                icon: Download,
                action: () => { onOpenDownloader(); onClose(); },
                category: 'element',
                keywords: ['add', 'video', 'download', 'youtube', 'url', 'stream']
            });
        }

        cmds.push({
            id: 'element.add.audio.upload',
            label: 'Add Audio (Upload)',
            description: 'Upload and add an audio file',
            icon: Volume2,
            action: () => { addAudioElement(); onClose(); },
            category: 'element',
            keywords: ['add', 'audio', 'upload', 'file', 'sound', 'music']
        });

        if (onOpenDownloader) {
            cmds.push({
                id: 'element.add.audio.download',
                label: 'Add Audio (Download)',
                description: 'Download audio from URL',
                icon: Download,
                action: () => { onOpenDownloader(); onClose(); },
                category: 'element',
                keywords: ['add', 'audio', 'download', 'youtube', 'url', 'music']
            });
        }

        cmds.push({
            id: 'element.add.stream',
            label: 'Add Live Stream',
            description: 'Add a live stream element',
            icon: Radio,
            action: () => { addLiveStreamElement(); onClose(); },
            category: 'element',
            keywords: ['add', 'stream', 'live', 'broadcast']
        });

        // View commands
        cmds.push({
            id: 'view.center',
            label: 'Center Canvas',
            description: 'Center the canvas view',
            icon: Maximize,
            action: () => { onCenter(); onClose(); },
            category: 'view',
            keywords: ['center', 'view']
        });

        if (onZoomToFit) {
            cmds.push({
                id: 'view.fit',
                label: 'Fit to Window',
                description: 'Reset zoom to fit canvas in window',
                icon: Maximize,
                action: () => { onZoomToFit(); onClose(); },
                category: 'view',
                keywords: ['fit', 'zoom', 'reset', 'window', 'scale']
            });
        }

        return cmds;
    }, [editorState.scenes, activeScene, selectedElement]);

    // Filter commands based on search
    const filteredCommands = useMemo(() => {
        if (!searchQuery.trim()) return commands;

        const query = searchQuery.toLowerCase().trim();
        return commands.filter(cmd => {
            const matchesLabel = cmd.label.toLowerCase().includes(query);
            const matchesDescription = cmd.description.toLowerCase().includes(query);
            const matchesKeywords = cmd.keywords?.some(keyword => keyword.includes(query)) || false;
            
            return matchesLabel || matchesDescription || matchesKeywords;
        });
    }, [commands, searchQuery]);

    // Keyboard navigation
    useEffect(() => {
        if (!isOpen) return;

        const handleKeyDown = (e: KeyboardEvent) => {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    setSelectedIndex(prev => Math.min(prev + 1, filteredCommands.length - 1));
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    setSelectedIndex(prev => Math.max(prev - 1, 0));
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (filteredCommands[selectedIndex]) {
                        filteredCommands[selectedIndex].action();
                    }
                    break;
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [isOpen, filteredCommands, selectedIndex]);

    // Reset selection when search changes
    useEffect(() => {
        setSelectedIndex(0);
    }, [searchQuery]);

    const getCategoryColor = (category: Command['category']) => {
        switch (category) {
            case 'file': return 'text-blue-400';
            case 'edit': return 'text-green-400';
            case 'scene': return 'text-purple-400';
            case 'element': return 'text-orange-400';
            case 'view': return 'text-cyan-400';
            default: return 'text-muted-foreground';
        }
    };

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
                        <DialogContent 
                className="max-w-2xl p-0 border-border/50" 
                style={{ backgroundColor: 'hsl(var(--background))' }}
            >
                <DialogTitle className="sr-only">Command Palette</DialogTitle>
                <DialogDescription className="sr-only">
                    Search and execute commands quickly using the keyboard
                </DialogDescription>
                
                {/* Search Input */}
                <div className="flex items-center border-b border-border/30 px-4 py-3">
                    <Search className="h-4 w-4 text-muted-foreground mr-3" />
                    <Input
                        placeholder="Type a command or search..."
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        className="border-0 focus:ring-0 focus:outline-none bg-transparent text-base"
                        autoFocus
                    />
                </div>

                {/* Commands List */}
                <div className="max-h-96 overflow-y-auto">
                    {filteredCommands.length === 0 ? (
                        <div className="p-8 text-center text-muted-foreground">
                            No commands found for "{searchQuery}"
                        </div>
                    ) : (
                        <div className="p-2">
                            {filteredCommands.map((command, index) => {
                                const Icon = command.icon;
                                return (
                                    <Button
                                        key={command.id}
                                        variant={index === selectedIndex ? 'secondary' : 'ghost'}
                                        className={`w-full justify-start h-auto p-3 mb-1 ${
                                            index === selectedIndex ? 'bg-accent' : ''
                                        }`}
                                        onClick={command.action}
                                        disabled={command.disabled}
                                    >
                                        <Icon className={`h-4 w-4 mr-3 ${getCategoryColor(command.category)}`} />
                                        <div className="flex flex-col items-start">
                                            <span className="font-medium">{command.label}</span>
                                            <span className="text-xs text-muted-foreground">
                                                {command.description}
                                            </span>
                                        </div>
                                    </Button>
                                );
                            })}
                        </div>
                    )}
                </div>

                {/* Footer */}
                <div className="p-3 border-t border-border/30 text-xs text-muted-foreground flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <span className="flex items-center gap-1">
                            <ArrowUp className="h-3 w-3" />
                            <ArrowDown className="h-3 w-3" />
                            Navigate
                        </span>
                        <span>↵ Select</span>
                        <span>Esc Close</span>
                    </div>
                    <span>{filteredCommands.length} commands</span>
                </div>
            </DialogContent>
        </Dialog>
    );
};