import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Plus, X, MoreHorizontal, Play, Square, Save, FolderOpen, Send } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import type { Scene } from '@shared/types';

interface SceneTabsProps {
    scenes: Scene[];
    activeScene: Scene | undefined;
    selectScene: (sceneId: string) => void;
    createNewScene: () => void;
    deleteScene: (sceneId: string) => void;
    duplicateScene: (sceneId: string) => void;
    renameScene: (sceneId: string, name: string) => void;
    previewMode?: boolean;
    setPreviewMode?: (mode: boolean) => void;
    onSave?: () => void;
    onLoad?: () => void;
    publishedScene?: Scene;
    onPublish?: (sceneId: string) => void;
}

export const SceneTabs: React.FC<SceneTabsProps> = ({
    scenes,
    activeScene,
    selectScene,
    createNewScene,
    deleteScene,
    duplicateScene,
    renameScene,
    previewMode,
    setPreviewMode,
    onSave,
    onLoad,
    publishedScene,
    onPublish
}) => {
    const [editingTab, setEditingTab] = useState<string | null>(null);
    const [tabName, setTabName] = useState('');

    const handleTabDoubleClick = (scene: Scene) => {
        setEditingTab(scene.id);
        setTabName(scene.name);
    };

    const handleTabNameSubmit = (sceneId: string) => {
        if (tabName.trim() && tabName !== scenes.find(s => s.id === sceneId)?.name) {
            renameScene(sceneId, tabName.trim());
        }
        setEditingTab(null);
        setTabName('');
    };

    const handleKeyDown = (e: React.KeyboardEvent, sceneId: string) => {
        if (e.key === 'Enter') {
            handleTabNameSubmit(sceneId);
        } else if (e.key === 'Escape') {
            setEditingTab(null);
            setTabName('');
        }
    };

    return (
        <div className="absolute top-0 left-0 right-0 h-8 bg-background/80 backdrop-blur-sm border-b border-border/30 flex items-center z-50">
            {/* Scene Tabs */}
            <div className="flex-1 flex items-center overflow-x-auto scrollbar-none">
                {scenes.map((scene) => (
                    <div
                        key={scene.id}
                        className={`group relative flex items-center min-w-0 max-w-48 ${
                            activeScene?.id === scene.id
                                ? 'bg-accent border-b-2 border-b-primary'
                                : 'hover:bg-accent/50'
                        }`}
                    >
                        {editingTab === scene.id ? (
                            <input
                                type="text"
                                value={tabName}
                                onChange={(e) => setTabName(e.target.value)}
                                onBlur={() => handleTabNameSubmit(scene.id)}
                                onKeyDown={(e) => handleKeyDown(e, scene.id)}
                                className="bg-transparent px-3 py-1 text-xs font-medium focus:outline-none w-full"
                                autoFocus
                            />
                        ) : (
                            <button
                                onClick={() => selectScene(scene.id)}
                                onDoubleClick={() => !previewMode && handleTabDoubleClick(scene)}
                                className="flex items-center gap-2 px-3 py-1 text-xs font-medium truncate w-full text-left"
                            >
                                <span className="truncate">{scene.name}</span>
                                {/* Status indicator: Red dot = Live, Orange dot = Live but modified */}
                                {!scene.isDraft && (
                                    <div className="w-1.5 h-1.5 bg-red-500 rounded-full flex-shrink-0" title="Live" />
                                )}
                                {scene.isDraft && publishedScene?.id === scene.id && (
                                    <div className="w-1.5 h-1.5 bg-orange-400 rounded-full flex-shrink-0" title="Live but modified" />
                                )}
                            </button>
                        )}
                        
                        {/* Close button - appears on hover */}
                        {!previewMode && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="absolute -right-1 top-0.5 w-4 h-4 p-0 opacity-0 group-hover:opacity-100 hover:bg-destructive/20"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    deleteScene(scene.id);
                                }}
                            >
                                <X className="h-2.5 w-2.5" />
                            </Button>
                        )}
                    </div>
                ))}
                
                {/* Add New Scene Tab */}
                {!previewMode && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={createNewScene}
                        className="h-7 w-7 p-0 mx-1 hover:bg-accent flex-shrink-0"
                    >
                        <Plus className="h-3 w-3" />
                    </Button>
                )}
            </div>

            {/* Right Side Controls */}
            <div className="flex items-center gap-1 px-2 border-l border-border/30">
                {/* Publish Button */}
                {!previewMode && activeScene && onPublish && (
                    <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onPublish(activeScene.id)}
                        className="h-6 w-6 p-0 hover:bg-accent"
                        title="Publish Scene"
                    >
                        <Send className="h-3 w-3" />
                    </Button>
                )}

                {/* Preview Mode Toggle */}
                <Button
                    variant={previewMode ? "default" : "ghost"}
                    size="sm"
                    onClick={() => setPreviewMode && setPreviewMode(!previewMode)}
                    className={`h-6 w-6 p-0 ${
                        previewMode 
                            ? "bg-green-600 hover:bg-green-700 text-white" 
                            : "hover:bg-accent"
                    }`}
                    title={previewMode ? "Exit Live Preview" : "Live Preview"}
                    disabled={!setPreviewMode}
                >
                    {previewMode ? (
                        <Square className="h-3 w-3" />
                    ) : (
                        <Play className="h-3 w-3" />
                    )}
                </Button>

                {/* More Actions */}
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                            <MoreHorizontal className="h-3 w-3" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                        <DropdownMenuItem 
                            onClick={() => activeScene && duplicateScene(activeScene.id)}
                            disabled={!activeScene || previewMode}
                        >
                            Duplicate Scene
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                            onClick={() => activeScene && handleTabDoubleClick(activeScene)}
                            disabled={!activeScene || previewMode}
                        >
                            Rename Scene
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        {onSave && (
                            <DropdownMenuItem onClick={onSave}>
                                <Save className="h-3 w-3 mr-2" />
                                Save Project
                            </DropdownMenuItem>
                        )}
                        {onLoad && (
                            <DropdownMenuItem onClick={onLoad}>
                                <FolderOpen className="h-3 w-3 mr-2" />
                                Load Project
                            </DropdownMenuItem>
                        )}
                    </DropdownMenuContent>
                </DropdownMenu>

            </div>
        </div>
    );
};