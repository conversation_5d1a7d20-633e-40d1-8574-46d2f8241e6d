{"compilerOptions": {"target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "lib": ["ES2022", "WebWorker"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowSyntheticDefaultImports": true, "types": ["@cloudflare/workers-types"]}, "include": ["src/**/*", "../shared/**/*"], "exclude": ["node_modules", "dist"]}