/* Global styles */
* {
  box-sizing: border-box;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #2a2a2c;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #484850 0%, #424244 100%);
  border-radius: 4px;
  border: 1px solid #3a3a3c;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a5a62 0%, #545456 100%);
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Inter', sans-serif;
  background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 100%);
  color: #e8e8e8;
  overflow: hidden;
  line-height: 1.4;
}

/* Layout */
.app {
  display: flex;
  height: 100vh;
  width: 100vw;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.canvas-container {
  flex: 1;
  background: linear-gradient(135deg, #131315 0%, #1a1a1c 100%);
  position: relative;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(120, 119, 198, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 118, 117, 0.05) 0%, transparent 50%);
}

.canvas {
  position: relative;
  background: #000000;
  border: 2px solid #4a4a4c;
  border-radius: 8px;
  transform-origin: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}


.scenes-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.scene-item {
  padding: 12px 16px;
  background: linear-gradient(135deg, #3a3a3c 0%, #343436 100%);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid transparent;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.scene-item:hover {
  background: linear-gradient(135deg, #484850 0%, #424244 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.scene-item.active {
  background: linear-gradient(135deg, #007acc 0%, #005a9e 100%);
  border-color: #0078d4;
  box-shadow: 0 4px 12px rgba(0, 120, 212, 0.3);
}

.scene-name {
  font-size: 14px;
  font-weight: 500;
}

/* Elements */
.elements-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.element-item {
  padding: 10px 14px;
  background: linear-gradient(135deg, #3a3a3c 0%, #343436 100%);
  border-radius: 6px;
  cursor: pointer;
  font-size: 13px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid transparent;
}

.element-item:hover {
  background: linear-gradient(135deg, #484850 0%, #424244 100%);
  transform: translateX(2px);
}

.element-item.selected {
  background: linear-gradient(135deg, #007acc 0%, #005a9e 100%);
  border-color: #0078d4;
  box-shadow: 0 2px 8px rgba(0, 120, 212, 0.3);
}

/* Canvas elements */
.canvas-element {
  position: absolute;
  cursor: move;
  user-select: none;
  border: 2px solid transparent;
  border-radius: 4px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.canvas-element.selected {
  border-color: #0078d4;
  box-shadow: 
    0 0 0 1px rgba(0, 120, 212, 0.3),
    0 4px 12px rgba(0, 120, 212, 0.2);
}

.canvas-element.selected::after {
  content: '';
  position: absolute;
  top: -6px;
  right: -6px;
  width: 12px;
  height: 12px;
  background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
  border: 2px solid white;
  border-radius: 50%;
  cursor: nw-resize;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.text-element {
  white-space: nowrap;
}

.image-element,
.video-element {
  display: block;
  max-width: 100%;
  max-height: 100%;
}

/* Buttons */
.button {
  padding: 10px 20px;
  background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.2);
  border: 1px solid transparent;
}

.button:hover {
  background: linear-gradient(135deg, #106ebe 0%, #0066b8 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 120, 212, 0.3);
}

.button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.2);
}

.button.secondary {
  background: linear-gradient(135deg, #3a3a3c 0%, #343436 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.button.secondary:hover {
  background: linear-gradient(135deg, #484850 0%, #424244 100%);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.button.small {
  padding: 6px 12px;
  font-size: 12px;
}

.button:disabled {
  background: linear-gradient(135deg, #2a2a2c 0%, #242426 100%);
  color: #666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.button:disabled:hover {
  background: linear-gradient(135deg, #2a2a2c 0%, #242426 100%);
  transform: none;
  box-shadow: none;
}

/* Properties panel */
.properties-panel {
  max-height: 400px;
  overflow-y: auto;
}

.property-group {
  margin-bottom: 16px;
}

.property-label {
  display: block;
  margin-bottom: 4px;
  font-size: 12px;
  color: #cccccc;
}

.property-input {
  width: 100%;
  padding: 6px 8px;
  background: #404040;
  border: 1px solid #555555;
  border-radius: 4px;
  color: white;
  font-size: 14px;
}

.property-input:focus {
  outline: none;
  border-color: #0078d4;
}

.property-row {
  display: flex;
  gap: 8px;
}

.property-row .property-input {
  flex: 1;
}

/* Color picker */
.color-input {
  width: 40px;
  height: 32px;
  padding: 0;
  border: 1px solid #555555;
  border-radius: 4px;
  background: none;
  cursor: pointer;
}

/* Add element buttons */
.add-element-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.add-element-buttons .button {
  justify-self: stretch;
  padding: 12px 16px;
  font-weight: 500;
}

/* Zoom Controls */
.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.zoom-controls input[type="range"] {
  width: 120px;
  height: 4px;
  background: #404040;
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.zoom-controls input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}

.zoom-controls input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}

/* Element Properties Panel */
.element-properties {
  margin-top: 24px;
  border-top: 1px solid #3a3a3c;
  padding-top: 0;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 8px;
  margin-left: -20px;
  margin-right: -20px;
  padding-left: 20px;
  padding-right: 20px;
}


.element-properties .property-group {
  margin-bottom: 18px;
}

.element-properties .property-group label {
  display: block;
  margin-bottom: 10px;
  font-size: 13px;
  color: #d0d0d2;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.element-properties input[type="range"] {
  width: 100%;
  height: 6px;
  background: linear-gradient(90deg, #404040 0%, #4a4a4c 100%);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
  border: 1px solid #333;
}

.element-properties input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}

.element-properties input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: linear-gradient(135deg, #0078d4 0%, #005a9e 100%);
  border: 2px solid white;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 120, 212, 0.3);
}
