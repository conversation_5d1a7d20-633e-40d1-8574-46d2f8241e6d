import { useState, useCallback } from 'react';

interface PanState {
    x: number;
    y: number;
    scrollLeft: number;
    scrollTop: number;
}

export const useCanvasInteraction = () => {
    const [isDragging, setIsDragging] = useState(false);
    const [isPanning, setIsPanning] = useState(false);
    const [panStart, setPanStart] = useState<PanState>({ x: 0, y: 0, scrollLeft: 0, scrollTop: 0 });

    const handleCanvasPanStart = useCallback((e: React.MouseEvent) => {
        if (e.target === e.currentTarget) {
            setIsPanning(true);
            const container = document.querySelector('[style*="overflow"]') as HTMLElement;
            if (container) {
                setPanStart({
                    x: e.clientX,
                    y: e.clientY,
                    scrollLeft: container.scrollLeft,
                    scrollTop: container.scrollTop
                });
            }
            e.preventDefault();
        }
    }, []);

    const handleCanvasPanMove = useCallback((e: React.MouseEvent) => {
        if (!isPanning) return;
        
        const container = document.querySelector('[style*="overflow"]') as HTMLElement;
        if (container) {
            const deltaX = e.clientX - panStart.x;
            const deltaY = e.clientY - panStart.y;
            
            container.scrollLeft = panStart.scrollLeft - deltaX;
            container.scrollTop = panStart.scrollTop - deltaY;
        }
        e.preventDefault();
    }, [isPanning, panStart]);

    const handleCanvasPanEnd = useCallback(() => {
        setIsPanning(false);
    }, []);

    return {
        isDragging,
        setIsDragging,
        isPanning,
        handleCanvasPanStart,
        handleCanvasPanMove,
        handleCanvasPanEnd
    };
};
