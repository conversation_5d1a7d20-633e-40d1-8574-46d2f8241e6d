import React from 'react';
import { Element } from '@shared/types';

interface ResizeHandleProps {
  element: Element;
  onResizeStart: (e: React.MouseEvent, element: Element) => void;
}

export const ResizeHandle: React.FC<ResizeHandleProps> = ({ element, onResizeStart }) => {
  return (
    <div
      className="absolute -bottom-2 -right-2 w-4 h-4 bg-primary border-2 border-white rounded-full cursor-nw-resize shadow-lg hover:scale-110 transition-all duration-150 z-10"
      onMouseDown={(e) => onResizeStart(e, element)}
      style={{
        transform: `scale(${1 / (element.transform.scaleX || 1)}, ${1 / (element.transform.scaleY || 1)})`,
        transformOrigin: 'center'
      }}
      title="Drag to resize"
    >
      {/* Visual grip */}
      <div className="absolute inset-1 bg-white rounded-full opacity-80" />
    </div>
  );
};