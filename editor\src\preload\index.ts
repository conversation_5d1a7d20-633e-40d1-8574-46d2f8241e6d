import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';
import { EditorState, Scene } from '@shared/types';

const electronAPI = {
  // State management
  getEditorState: (): Promise<EditorState> => ipcRenderer.invoke('get-editor-state'),
  updateScene: (scene: Scene): Promise<EditorState> => ipcRenderer.invoke('update-scene', scene),
  deleteScene: (sceneId: string): Promise<EditorState> => ipcRenderer.invoke('delete-scene', sceneId),
  setActiveScene: (sceneId: string): Promise<EditorState> => ipcRenderer.invoke('set-active-scene', sceneId),
  publishScene: (sceneId: string): Promise<{success: boolean, scene?: Scene, error?: string}> => ipcRenderer.invoke('publish-scene', sceneId),
  
  // Debug functions
  clearAllScenes: (): Promise<EditorState> => ipcRenderer.invoke('clear-all-scenes'),
  refreshEditorState: (): Promise<EditorState> => ipc<PERSON>ender<PERSON>.invoke('get-editor-state'),
  
  // File operations
  saveProject: (): Promise<{success: boolean, path?: string, error?: string}> => ipcR<PERSON>er.invoke('save-project'),
  loadProject: (): Promise<{success: boolean, state?: EditorState, error?: string}> => ipcRenderer.invoke('load-project'),
  
  // Media upload
  uploadMedia: (fileBuffer: Uint8Array, originalName: string, mimeType: string): Promise<{success: boolean, url?: string, error?: string}> => 
    ipcRenderer.invoke('upload-media', fileBuffer, originalName, mimeType),
  
  // WebSocket management
  setWebSocketUrl: (url: string): Promise<{success: boolean, url: string}> => ipcRenderer.invoke('set-websocket-url', url),
  getWebSocketStatus: (): Promise<{connected: boolean, url: string}> => ipcRenderer.invoke('get-websocket-status'),
  
  // Video download
  downloadVideo: (url: string, options?: {
    outputPath?: string, 
    useCookies?: boolean,
    extractAudio?: boolean,
    audioFormat?: string,
    format?: string,
    audioOnly?: boolean
  }): Promise<{success: boolean, title?: string, outputPath?: string, message?: string, error?: string}> => 
    ipcRenderer.invoke('download-video', url, options),
  getYtDlpStatus: (): Promise<{ready: boolean}> => ipcRenderer.invoke('get-ytdlp-status'),
  
  // Livestream URL extraction
  extractStreamUrl: (url: string, useCookies?: boolean): Promise<{success: boolean, streamUrl?: string, platform?: string, title?: string, error?: string}> =>
    ipcRenderer.invoke('extract-stream-url', url, useCookies),
};

contextBridge.exposeInMainWorld('electronAPI', electronAPI);

export type ElectronAPI = typeof electronAPI;
