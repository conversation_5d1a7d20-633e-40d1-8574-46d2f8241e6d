import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu';
import { Plus, Image, Video, Volume2, Radio, Eye, EyeOff, MoreVertical, Edit2, Trash2, Layers, Lock, Unlock, Users, ChevronDown, ChevronRight, ArrowUp, ArrowDown, MoveUp, MoveDown, Upload, Download } from 'lucide-react';
import { Scene, Element, TextElement, ElementGroup } from '@shared/types';
import { MediaDownloader } from '../MediaDownloader';

interface ElementListProps {
    activeScene: Scene;
    selectedElement: Element | null;
    previewMode: boolean;
    editingElementId: string | null;
    setEditingElementId: (id: string | null) => void;
    setSelectedElement: (element: Element | null) => void;
    addTextElement: () => void;
    addImageElement: () => void;
    addVideoElement: () => void;
    addAudioElement: () => void;
    addLiveStreamElement: (streamData: {
        originalUrl: string;
        streamUrl: string;
        platform: string;
        title: string;
    }) => void;
    deleteElement: (id: string) => void;
    toggleElementVisibility: (id: string) => void;
    renameElement: (id: string, name: string) => void;
    // Layering functions
    bringToFront: (elementId: string) => void;
    sendToBack: (elementId: string) => void;
    bringForward: (elementId: string) => void;
    sendBackward: (elementId: string) => void;
    // Grouping functions
    createGroup: (elementIds: string[], groupName?: string) => void;
    ungroupElements: (groupId: string) => void;
    addToGroup: (groupId: string, elementIds: string[]) => void;
    removeFromGroup: (groupId: string, elementIds: string[]) => void;
    toggleGroupVisibility: (groupId: string) => void;
    toggleGroupLock: (groupId: string) => void;
    renameGroup: (groupId: string, newName: string) => void;
}

export const ElementList: React.FC<ElementListProps> = ({
    activeScene,
    selectedElement,
    previewMode,
    editingElementId,
    setEditingElementId,
    setSelectedElement,
    addTextElement,
    addImageElement,
    addVideoElement,
    addAudioElement,
    addLiveStreamElement,
    deleteElement,
    toggleElementVisibility,
    renameElement,
    bringToFront,
    sendToBack,
    bringForward,
    sendBackward,
    createGroup,
    ungroupElements,
    addToGroup,
    removeFromGroup,
    toggleGroupVisibility,
    toggleGroupLock,
    renameGroup
}) => {
    const [selectedElements, setSelectedElements] = useState<string[]>([]);
    const [collapsedGroups, setCollapsedGroups] = useState<string[]>([]);
    const [editingGroupId, setEditingGroupId] = useState<string | null>(null);
    const [isLiveStreamModalOpen, setIsLiveStreamModalOpen] = useState(false);
    const [streamUrl, setStreamUrl] = useState('');
    const [isExtractingStream, setIsExtractingStream] = useState(false);
    const [useCookies, setUseCookies] = useState(false);
    const [isVideoDownloadModalOpen, setIsVideoDownloadModalOpen] = useState(false);
    const [isAudioDownloadModalOpen, setIsAudioDownloadModalOpen] = useState(false);

    const handleElementSelect = (element: Element, ctrlKey: boolean = false) => {
        if (ctrlKey) {
            // Multi-select
            if (selectedElements.includes(element.id)) {
                setSelectedElements(selectedElements.filter(id => id !== element.id));
            } else {
                setSelectedElements([...selectedElements, element.id]);
            }
        } else {
            // Single select
            setSelectedElement(element);
            setSelectedElements([element.id]);
        }
    };

    const handleGroupCreate = () => {
        if (selectedElements.length > 1) {
            createGroup(selectedElements);
            setSelectedElements([]);
        }
    };

    const toggleGroupCollapse = (groupId: string) => {
        if (collapsedGroups.includes(groupId)) {
            setCollapsedGroups(collapsedGroups.filter(id => id !== groupId));
        } else {
            setCollapsedGroups([...collapsedGroups, groupId]);
        }
    };

    const handleAddLiveStream = async () => {
        if (!streamUrl.trim()) return;
        
        setIsExtractingStream(true);
        try {
            const result = await window.electronAPI.extractStreamUrl(streamUrl, useCookies);
            
            if (result.success && result.streamUrl) {
                addLiveStreamElement({
                    originalUrl: streamUrl,
                    streamUrl: result.streamUrl,
                    platform: result.platform || 'other',
                    title: result.title || 'Live Stream'
                });
                
                setIsLiveStreamModalOpen(false);
                setStreamUrl('');
            } else {
                alert(result.error || 'Failed to extract stream URL');
            }
        } catch (error) {
            alert('An error occurred while extracting the stream URL');
        } finally {
            setIsExtractingStream(false);
        }
    };

    // Group elements by their group membership
    const groupedElements = new Map<string | null, Element[]>();
    const groups = activeScene.groups || [];
    
    // Initialize groups
    groups.forEach(group => {
        groupedElements.set(group.id, []);
    });
    groupedElements.set(null, []); // Ungrouped elements

    // Categorize elements
    activeScene.elements.forEach(element => {
        const group = groups.find(g => g.elementIds.includes(element.id));
        const groupId = group?.id || null;
        const existingElements = groupedElements.get(groupId) || [];
        groupedElements.set(groupId, [...existingElements, element]);
    });

    // Sort elements by zIndex within each group
    groupedElements.forEach((elements, groupId) => {
        elements.sort((a, b) => b.zIndex - a.zIndex);
    });
    return (
        <div className="space-y-4">
            {!previewMode && (
                <div className="space-y-2 mb-4">
                    {selectedElements.length > 1 && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 text-center mb-2">
                            <div className="text-blue-700 text-xs font-medium">
                                {selectedElements.length} elements selected
                            </div>
                            <div className="text-blue-600 text-xs">
                                Ctrl+click to add/remove from selection
                            </div>
                        </div>
                    )}
                    
                    <div className="grid grid-cols-1 gap-2">
                        <Button onClick={addTextElement} variant="outline" size="sm">
                            <Plus className="w-4 h-4 mr-2" />
                            Add Text
                        </Button>
                        <Button onClick={addImageElement} variant="outline" size="sm">
                            <Image className="w-4 h-4 mr-2" />
                            Add Image
                        </Button>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm">
                                    <Video className="w-4 h-4 mr-2" />
                                    Add Video
                                    <ChevronDown className="w-3 h-3 ml-1" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-white dark:bg-gray-800">
                                <DropdownMenuItem onClick={addVideoElement}>
                                    <Upload className="w-4 h-4 mr-2" />
                                    Upload Video
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => setIsVideoDownloadModalOpen(true)}>
                                    <Download className="w-4 h-4 mr-2" />
                                    Download Video
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="outline" size="sm">
                                    <Volume2 className="w-4 h-4 mr-2" />
                                    Add Audio
                                    <ChevronDown className="w-3 h-3 ml-1" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent className="bg-white dark:bg-gray-800">
                                <DropdownMenuItem onClick={addAudioElement}>
                                    <Upload className="w-4 h-4 mr-2" />
                                    Upload Audio
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => setIsAudioDownloadModalOpen(true)}>
                                    <Download className="w-4 h-4 mr-2" />
                                    Download Audio
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                        <Button onClick={() => setIsLiveStreamModalOpen(true)} variant="outline" size="sm">
                            <Radio className="w-4 h-4 mr-2" />
                            Add Live Stream
                        </Button>
                    </div>
                    
                    {selectedElements.length > 1 && (
                        <Button onClick={handleGroupCreate} variant="outline" size="sm" className="w-full">
                            <Users className="w-4 h-4 mr-2" />
                            Group Selected ({selectedElements.length})
                        </Button>
                    )}
                </div>
            )}
            
            <div className="space-y-1">
                {/* Render Groups */}
                {groups.map(group => {
                    const groupElements = groupedElements.get(group.id) || [];
                    const isCollapsed = collapsedGroups.includes(group.id);
                    
                    return (
                        <div key={group.id} className="space-y-1">
                            {/* Group Header */}
                            <div className="flex items-center gap-1 p-2 bg-muted/50 rounded-md">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => toggleGroupCollapse(group.id)}
                                >
                                    {isCollapsed ? (
                                        <ChevronRight className="w-3 h-3" />
                                    ) : (
                                        <ChevronDown className="w-3 h-3" />
                                    )}
                                </Button>
                                
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => toggleGroupVisibility(group.id)}
                                >
                                    {group.visible ? (
                                        <Eye className="w-3 h-3" />
                                    ) : (
                                        <EyeOff className="w-3 h-3 text-muted-foreground" />
                                    )}
                                </Button>
                                
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                    onClick={() => toggleGroupLock(group.id)}
                                >
                                    {group.locked ? (
                                        <Lock className="w-3 h-3" />
                                    ) : (
                                        <Unlock className="w-3 h-3" />
                                    )}
                                </Button>
                                
                                <div className="flex-1 min-w-0">
                                    {editingGroupId === group.id ? (
                                        <Input
                                            defaultValue={group.name}
                                            className="text-xs border-none p-0 h-auto bg-transparent"
                                            onBlur={(e) => {
                                                renameGroup(group.id, e.target.value);
                                                setEditingGroupId(null);
                                            }}
                                            onKeyDown={(e) => {
                                                if (e.key === 'Enter') {
                                                    renameGroup(group.id, e.currentTarget.value);
                                                    setEditingGroupId(null);
                                                } else if (e.key === 'Escape') {
                                                    setEditingGroupId(null);
                                                }
                                            }}
                                            autoFocus
                                        />
                                    ) : (
                                        <span 
                                            className="text-xs font-medium truncate cursor-pointer"
                                            onDoubleClick={() => !previewMode && setEditingGroupId(group.id)}
                                        >
                                            {group.name}
                                        </span>
                                    )}
                                </div>
                                
                                <Badge variant="outline" className="text-xs">
                                    {groupElements.length}
                                </Badge>
                                
                                {!previewMode && (
                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                                                <MoreVertical className="w-3 h-3" />
                                            </Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent align="end">
                                            <DropdownMenuItem onClick={() => setEditingGroupId(group.id)}>
                                                <Edit2 className="w-4 h-4 mr-2" />
                                                Rename Group
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => toggleGroupVisibility(group.id)}>
                                                {group.visible ? (
                                                    <>
                                                        <EyeOff className="w-4 h-4 mr-2" />
                                                        Hide Group
                                                    </>
                                                ) : (
                                                    <>
                                                        <Eye className="w-4 h-4 mr-2" />
                                                        Show Group
                                                    </>
                                                )}
                                            </DropdownMenuItem>
                                            <DropdownMenuItem onClick={() => toggleGroupLock(group.id)}>
                                                {group.locked ? (
                                                    <>
                                                        <Unlock className="w-4 h-4 mr-2" />
                                                        Unlock Group
                                                    </>
                                                ) : (
                                                    <>
                                                        <Lock className="w-4 h-4 mr-2" />
                                                        Lock Group
                                                    </>
                                                )}
                                            </DropdownMenuItem>
                                            <DropdownMenuSeparator />
                                            <DropdownMenuItem onClick={() => ungroupElements(group.id)}>
                                                <Users className="w-4 h-4 mr-2" />
                                                Ungroup
                                            </DropdownMenuItem>
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                )}
                            </div>
                            
                            {/* Group Elements */}
                            {!isCollapsed && (
                                <div className="ml-4 space-y-1">
                                    {groupElements.map(element => (
                                        <ElementItem 
                                            key={element.id}
                                            element={element}
                                            selectedElement={selectedElement}
                                            selectedElements={selectedElements}
                                            editingElementId={editingElementId}
                                            previewMode={previewMode}
                                            onElementSelect={handleElementSelect}
                                            setEditingElementId={setEditingElementId}
                                            toggleElementVisibility={toggleElementVisibility}
                                            renameElement={renameElement}
                                            deleteElement={deleteElement}
                                            bringToFront={bringToFront}
                                            sendToBack={sendToBack}
                                            bringForward={bringForward}
                                            sendBackward={sendBackward}
                                        />
                                    ))}
                                </div>
                            )}
                        </div>
                    );
                })}
                
                {/* Ungrouped Elements */}
                {(groupedElements.get(null) || []).map(element => (
                    <ElementItem 
                        key={element.id}
                        element={element}
                        selectedElement={selectedElement}
                        selectedElements={selectedElements}
                        editingElementId={editingElementId}
                        previewMode={previewMode}
                        onElementSelect={handleElementSelect}
                        setEditingElementId={setEditingElementId}
                        toggleElementVisibility={toggleElementVisibility}
                        renameElement={renameElement}
                        deleteElement={deleteElement}
                        bringToFront={bringToFront}
                        sendToBack={sendToBack}
                        bringForward={bringForward}
                        sendBackward={sendBackward}
                    />
                ))}
            </div>
            
            {/* Live Stream Modal */}
            {isLiveStreamModalOpen && (
                <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-[99999]" style={{ zIndex: 99999 }}>
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw] shadow-2xl border border-gray-200 dark:border-gray-600">
                        <div className="flex justify-between items-center mb-4">
                            <h2 className="text-lg font-semibold">Add Live Stream</h2>
                            <Button variant="outline" size="sm" onClick={() => setIsLiveStreamModalOpen(false)}>
                                ×
                            </Button>
                        </div>
                        
                        <div className="space-y-4">
                            <div>
                                <label className="block text-sm font-medium mb-2">
                                    Stream URL (YouTube, Twitch, Kick)
                                </label>
                                <Input
                                    type="url"
                                    placeholder="https://www.twitch.tv/streamer"
                                    value={streamUrl}
                                    onChange={(e) => setStreamUrl(e.target.value)}
                                    disabled={isExtractingStream}
                                    className="w-full"
                                    onKeyPress={(e) => {
                                        if (e.key === 'Enter' && !isExtractingStream) {
                                            handleAddLiveStream();
                                        }
                                    }}
                                />
                            </div>
                            
                            <div>
                                <label className="flex items-center space-x-2 text-sm">
                                    <input
                                        type="checkbox"
                                        checked={useCookies}
                                        onChange={(e) => setUseCookies(e.target.checked)}
                                        disabled={isExtractingStream}
                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700"
                                    />
                                    <span>Use cookies for authentication (YouTube login required)</span>
                                </label>
                            </div>
                            
                            <div className="text-xs text-gray-500 dark:text-gray-400">
                                <p>Live stream will be added directly to your canvas as a video element.</p>
                                <p>Supports YouTube Live, Twitch, Kick, and other live streaming platforms.</p>
                            </div>
                            
                            <div className="flex gap-3">
                                <Button
                                    onClick={handleAddLiveStream}
                                    disabled={isExtractingStream || !streamUrl.trim()}
                                    className="flex-1"
                                >
                                    {isExtractingStream ? (
                                        <>Loading...</>
                                    ) : (
                                        <>
                                            <Radio className="w-4 h-4 mr-2" />
                                            Add Live Stream
                                        </>
                                    )}
                                </Button>
                                <Button 
                                    variant="outline" 
                                    onClick={() => setIsLiveStreamModalOpen(false)}
                                    disabled={isExtractingStream}
                                >
                                    Cancel
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )}
            
            {/* Video Download Modal */}
            <MediaDownloader
                isOpen={isVideoDownloadModalOpen}
                onClose={() => setIsVideoDownloadModalOpen(false)}
                downloadType="video"
            />
            
            {/* Audio Download Modal */}
            <MediaDownloader
                isOpen={isAudioDownloadModalOpen}
                onClose={() => setIsAudioDownloadModalOpen(false)}
                downloadType="audio"
            />
        </div>
    );
};

// Element Item Component for reusability
interface ElementItemProps {
    element: Element;
    selectedElement: Element | null;
    selectedElements: string[];
    editingElementId: string | null;
    previewMode: boolean;
    onElementSelect: (element: Element, ctrlKey?: boolean) => void;
    setEditingElementId: (id: string | null) => void;
    toggleElementVisibility: (id: string) => void;
    renameElement: (id: string, name: string) => void;
    deleteElement: (id: string) => void;
    bringToFront: (elementId: string) => void;
    sendToBack: (elementId: string) => void;
    bringForward: (elementId: string) => void;
    sendBackward: (elementId: string) => void;
}

const ElementItem: React.FC<ElementItemProps> = ({
    element,
    selectedElement,
    selectedElements,
    editingElementId,
    previewMode,
    onElementSelect,
    setEditingElementId,
    toggleElementVisibility,
    renameElement,
    deleteElement,
    bringToFront,
    sendToBack,
    bringForward,
    sendBackward
}) => {
    const isSelected = element.id === selectedElement?.id;
    const isMultiSelected = selectedElements.includes(element.id);
    
    return (
        <div className="flex items-center gap-1">
            <Button
                variant={isSelected ? "default" : isMultiSelected ? "secondary" : "ghost"}
                className={`flex-1 justify-between h-auto p-2 min-h-[40px] transition-all ${
                    isSelected 
                        ? "bg-primary text-primary-foreground border-2 border-primary shadow-md" 
                        : isMultiSelected 
                            ? "bg-secondary text-secondary-foreground border-2 border-secondary shadow-sm" 
                            : "hover:bg-accent hover:text-accent-foreground border-2 border-transparent"
                }`}
                onClick={(e) => onElementSelect(element, e.ctrlKey)}
                onDoubleClick={() => !previewMode && setEditingElementId(element.id)}
            >
                <div className="flex items-center gap-2 min-w-0 flex-1">
                    <div
                        onClick={(e) => {
                            e.stopPropagation();
                            toggleElementVisibility(element.id);
                        }}
                        className="hover:text-primary transition-colors cursor-pointer flex-shrink-0"
                    >
                        {(element.visible ?? true) ? (
                            <Eye className="w-3 h-3" />
                        ) : (
                            <EyeOff className="w-3 h-3 text-muted-foreground" />
                        )}
                    </div>
                    
                    <div className="flex flex-col items-start min-w-0 flex-1">
                        {editingElementId === element.id ? (
                            <Input
                                defaultValue={element.name}
                                className="text-xs border-none p-0 h-auto bg-transparent"
                                onBlur={(e) => {
                                    renameElement(element.id, e.target.value);
                                    setEditingElementId(null);
                                }}
                                onKeyDown={(e) => {
                                    if (e.key === 'Enter') {
                                        renameElement(element.id, e.currentTarget.value);
                                        setEditingElementId(null);
                                    } else if (e.key === 'Escape') {
                                        setEditingElementId(null);
                                    }
                                }}
                                autoFocus
                                onClick={(e) => e.stopPropagation()}
                            />
                        ) : (
                            <span className="font-medium text-xs truncate">
                                {element.name}
                            </span>
                        )}
                        <span className="text-xs text-muted-foreground truncate">
                            {element.type === 'text' 
                                ? (element as TextElement).content.length > 15 
                                    ? (element as TextElement).content.substring(0, 15) + '...' 
                                    : (element as TextElement).content
                                : `${element.type} element`
                            }
                        </span>
                    </div>
                </div>
                
                <div className="flex items-center gap-1 flex-shrink-0">
                    <Badge variant="outline" className="text-xs">
                        {element.zIndex}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                        {element.type}
                    </Badge>
                </div>
            </Button>
            
            {!previewMode && (
                <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0 flex-shrink-0">
                            <MoreVertical className="w-3 h-3" />
                        </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => setEditingElementId(element.id)}>
                            <Edit2 className="w-4 h-4 mr-2" />
                            Rename
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => toggleElementVisibility(element.id)}>
                            {(element.visible ?? true) ? (
                                <>
                                    <EyeOff className="w-4 h-4 mr-2" />
                                    Hide
                                </>
                            ) : (
                                <>
                                    <Eye className="w-4 h-4 mr-2" />
                                    Show
                                </>
                            )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => bringToFront(element.id)}>
                            <ArrowUp className="w-4 h-4 mr-2" />
                            Bring to Front
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => bringForward(element.id)}>
                            <MoveUp className="w-4 h-4 mr-2" />
                            Bring Forward
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => sendBackward(element.id)}>
                            <MoveDown className="w-4 h-4 mr-2" />
                            Send Backward
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => sendToBack(element.id)}>
                            <ArrowDown className="w-4 h-4 mr-2" />
                            Send to Back
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => deleteElement(element.id)} className="text-destructive focus:text-destructive">
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                        </DropdownMenuItem>
                    </DropdownMenuContent>
                </DropdownMenu>
            )}
        </div>
    );
};