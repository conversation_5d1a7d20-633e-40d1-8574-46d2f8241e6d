import { useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Scene, EditorState } from '@shared/types';

interface UseSceneManagementProps {
    editorState: EditorState;
    setEditorState: (state: EditorState) => void;
    saveToHistory: (state: EditorState) => void;
    updateScene: (scene: Scene) => void;
    selectScene: (id: string) => void;
    api: any;
}

export const useSceneManagement = ({
    editorState,
    setEditorState,
    saveToHistory,
    updateScene,
    selectScene,
    api
}: UseSceneManagementProps) => {

    const createNewScene = useCallback(() => {
        saveToHistory(editorState);
        
        // Find the first available scene number (backfill gaps)
        const existingNumbers = editorState.scenes
            .map(scene => {
                const match = scene.name.match(/^Scene (\d+)$/);
                return match ? parseInt(match[1], 10) : 0;
            })
            .filter(num => num > 0)
            .sort((a, b) => a - b);
        
        let nextNumber = 1;
        for (const num of existingNumbers) {
            if (num === nextNumber) {
                nextNumber++;
            } else {
                break; // Found a gap, use nextNumber
            }
        }
        
        const newScene = {
            id: uuidv4(),
            name: `Scene ${nextNumber}`,
            elements: [],
            groups: [],
            canvasSize: editorState.canvasSize,
            isDraft: true
        };
        
        const updatedState = {
            ...editorState,
            scenes: [...editorState.scenes, newScene],
            activeSceneId: newScene.id
        };
        
        setEditorState(updatedState);
        api.updateScene(newScene);
        api.setActiveScene(newScene.id);
    }, [editorState, saveToHistory, setEditorState, api]);

    const deleteScene = useCallback((sceneId: string) => {
        saveToHistory(editorState);
        
        const updatedScenes = editorState.scenes.filter(s => s.id !== sceneId);
        const newActiveId = sceneId === editorState.activeSceneId 
            ? updatedScenes[0]?.id 
            : editorState.activeSceneId;
        
        const updatedState = {
            ...editorState,
            scenes: updatedScenes,
            activeSceneId: newActiveId
        };
        
        setEditorState(updatedState);
        api.deleteScene(sceneId);
        if (newActiveId) api.setActiveScene(newActiveId);
    }, [editorState, saveToHistory, setEditorState, api]);

    const duplicateScene = useCallback((sceneId: string) => {
        const sceneToDuplicate = editorState.scenes.find(s => s.id === sceneId);
        if (!sceneToDuplicate) return;
        
        saveToHistory(editorState);
        
        // Create a mapping from old element IDs to new element IDs
        const elementIdMapping = new Map<string, string>();
        const duplicatedElements = sceneToDuplicate.elements.map(el => {
            const newId = uuidv4();
            elementIdMapping.set(el.id, newId);
            return { ...el, id: newId };
        });

        const duplicatedScene = {
            ...JSON.parse(JSON.stringify(sceneToDuplicate)),
            id: uuidv4(),
            name: `${sceneToDuplicate.name} (Copy)`,
            elements: duplicatedElements,
            groups: (sceneToDuplicate.groups || []).map(group => ({
                ...group,
                id: uuidv4(),
                elementIds: group.elementIds.map(oldId => elementIdMapping.get(oldId) || oldId)
            }))
        };
        
        const updatedState = {
            ...editorState,
            scenes: [...editorState.scenes, duplicatedScene],
            activeSceneId: duplicatedScene.id
        };
        
        setEditorState(updatedState);
        api.updateScene(duplicatedScene);
        api.setActiveScene(duplicatedScene.id);
    }, [editorState, saveToHistory, setEditorState, api]);

    const publishScene = useCallback(async (sceneId: string) => {
        const result = await api.publishScene(sceneId);
        if (result.success) {
            // Update local state directly instead of reloading from persisted file
            const updatedState = {
                ...editorState,
                scenes: editorState.scenes.map(s => 
                    s.id === sceneId 
                        ? { ...s, isDraft: false, lastPublished: new Date() }
                        : { ...s, isDraft: true }
                ),
                publishedScene: result.scene,
                activeSceneId: sceneId
            };
            setEditorState(updatedState);
        }
        return result;
    }, [api, setEditorState, editorState]);

    const renameScene = useCallback((sceneId: string, newName: string) => {
        const updatedScenes = editorState.scenes.map(s => 
            s.id === sceneId ? { ...s, name: newName } : s
        );
        
        const updatedState = {
            ...editorState,
            scenes: updatedScenes
        };
        
        setEditorState(updatedState);
        
        const updatedScene = updatedScenes.find(s => s.id === sceneId);
        if (updatedScene) api.updateScene(updatedScene);
    }, [editorState, setEditorState, api]);

    return {
        createNewScene,
        deleteScene,
        duplicateScene,
        publishScene,
        renameScene
    };
};
