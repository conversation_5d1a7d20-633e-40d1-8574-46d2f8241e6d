import { useCallback } from 'react';
import { Element, Scene, EditorState } from '@shared/types';

interface UseLayeringProps {
    activeScene: Scene | null | undefined;
    selectedElement: Element | null;
    editorState: EditorState;
    saveToHistory: (state: EditorState) => void;
    updateScene: (scene: Scene) => void;
    setSelectedElement: (element: Element | null) => void;
}

export const useLayering = ({
    activeScene,
    selectedElement,
    editorState,
    saveToHistory,
    updateScene,
    setSelectedElement
}: UseLayeringProps) => {

    const bringToFront = useCallback((elementId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const maxZIndex = Math.max(...activeScene.elements.map(el => el.zIndex));
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, zIndex: maxZIndex + 1 }
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, zIndex: maxZIndex + 1 });
        }
    }, [activeScene, selectedElement, editorState, saveToHistory, updateScene, setSelectedElement]);

    const sendToBack = useCallback((elementId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const minZIndex = Math.min(...activeScene.elements.map(el => el.zIndex));
        
        const updatedElements = activeScene.elements.map(el => 
            el.id === elementId 
                ? { ...el, zIndex: minZIndex - 1 }
                : el
        );
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, zIndex: minZIndex - 1 });
        }
    }, [activeScene, selectedElement, editorState, saveToHistory, updateScene, setSelectedElement]);

    const bringForward = useCallback((elementId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const element = activeScene.elements.find(el => el.id === elementId);
        if (!element) return;
        
        // Find the next higher zIndex
        const higherElements = activeScene.elements
            .filter(el => el.zIndex > element.zIndex)
            .sort((a, b) => a.zIndex - b.zIndex);
        
        if (higherElements.length === 0) return; // Already at front
        
        const nextZIndex = higherElements[0].zIndex;
        
        const updatedElements = activeScene.elements.map(el => {
            if (el.id === elementId) {
                return { ...el, zIndex: nextZIndex };
            } else if (el.zIndex === nextZIndex) {
                return { ...el, zIndex: element.zIndex };
            }
            return el;
        });
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, zIndex: nextZIndex });
        }
    }, [activeScene, selectedElement, editorState, saveToHistory, updateScene, setSelectedElement]);

    const sendBackward = useCallback((elementId: string) => {
        if (!activeScene) return;
        
        saveToHistory(editorState);
        
        const element = activeScene.elements.find(el => el.id === elementId);
        if (!element) return;
        
        // Find the next lower zIndex
        const lowerElements = activeScene.elements
            .filter(el => el.zIndex < element.zIndex)
            .sort((a, b) => b.zIndex - a.zIndex);
        
        if (lowerElements.length === 0) return; // Already at back
        
        const nextZIndex = lowerElements[0].zIndex;
        
        const updatedElements = activeScene.elements.map(el => {
            if (el.id === elementId) {
                return { ...el, zIndex: nextZIndex };
            } else if (el.zIndex === nextZIndex) {
                return { ...el, zIndex: element.zIndex };
            }
            return el;
        });
        
        const updatedScene = {
            ...activeScene,
            elements: updatedElements
        };
        
        updateScene(updatedScene);
        
        if (selectedElement?.id === elementId) {
            setSelectedElement({ ...selectedElement, zIndex: nextZIndex });
        }
    }, [activeScene, selectedElement, editorState, saveToHistory, updateScene, setSelectedElement]);

    return {
        bringToFront,
        sendToBack,
        bringForward,
        sendBackward
    };
};
