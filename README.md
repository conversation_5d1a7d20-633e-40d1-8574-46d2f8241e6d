# OBS Browser Source Editor

A custom browser source system for OBS with live editing capabilities.

## Architecture

- **editor/**: Electron desktop application for designing layouts
- **browser-source/**: <PERSON>flare Worker hosting the browser source page
- **shared/**: Shared types and utilities

## Features

- Drag-and-drop layout editor
- Real-time preview
- Text overlays with custom styling
- Image and video display
- YouTube video integration (via yt-dlp)
- Live sync between editor and browser source

### 🆕 New Features

#### Element Layering
- **Bring to Front/Back**: Move elements to the very front or back of the layer stack
- **Bring Forward/Send Backward**: Move elements one layer up or down
- **Visual Z-Index**: See each element's layer position in the element list
- **Smart Layering**: Automatic layer management when creating new elements

#### Element Grouping
- **Group Elements**: Select multiple elements (Ctrl+Click) and group them together
- **Group Management**: Rename, hide/show, and lock/unlock entire groups
- **Collapsible Groups**: Expand/collapse groups in the element list for better organization
- **Group Visibility**: Toggle visibility of all elements in a group at once
- **Group Locking**: Lock groups to prevent accidental modifications

#### Enhanced Element List
- **Multi-Selection**: Hold Ctrl while clicking to select multiple elements
- **Hierarchical View**: See elements organized by groups with proper indentation
- **Layer Indicators**: Visual badges showing element z-index and type
- **Quick Actions**: Right-click context menus for layering and grouping operations

## Getting Started

1. Set up the editor: `cd editor && npm install && npm start`
2. Deploy browser source: `cd browser-source && npm install && npm run deploy`

## Development

The editor runs locally and communicates with the hosted browser source via WebSockets for real-time updates.

### 🏗️ Architecture & Code Organization

#### Modular Hook System
The application is built with a modular architecture using custom React hooks for better maintainability:

- **`useElementManagement`**: Handles all element CRUD operations (create, update, delete)
- **`useSceneManagement`**: Manages scene operations (create, duplicate, delete, publish)
- **`useLayering`**: Controls element z-index and layering operations
- **`useGrouping`**: Manages element grouping functionality
- **`useCanvasInteraction`**: Handles canvas panning and drag interactions
- **`useElementSelection`**: Manages element selection state and clipboard operations
- **`useEditorState`**: Core editor state management with undo/redo
- **`useZoom`**: Zoom and scaling functionality
- **`useKeyboardShortcuts`**: Keyboard shortcut handling

#### Benefits of Modular Design
- **Separation of Concerns**: Each hook handles a specific domain
- **Reusability**: Hooks can be easily reused across components
- **Testability**: Individual modules can be tested in isolation
- **Maintainability**: Easier to locate and modify specific functionality
- **Code Organization**: Cleaner, more readable codebase

## Usage Tips

### Grouping Workflow
1. Select multiple elements by holding Ctrl and clicking
2. Click "Group Selected" button to create a group
3. Use the group controls to manage visibility and locking
4. Right-click groups for additional options like ungrouping

### Layering Workflow
1. Right-click any element to access layering options
2. Use "Bring to Front" for elements that should be on top
3. Use "Send to Back" for background elements
4. Use "Bring Forward/Send Backward" for fine-tuned positioning
5. Check the z-index badges to see current layer positions
