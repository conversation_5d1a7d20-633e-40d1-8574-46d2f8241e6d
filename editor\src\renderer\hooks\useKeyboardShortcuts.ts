import { useEffect } from 'react';
import { Element, Scene, EditorState } from '@shared/types';

interface UseKeyboardShortcutsProps {
    copyElement: () => void;
    pasteElement: () => void;
    duplicateElement: () => void;
    selectedElement: Element | null;
    activeScene: Scene | undefined;
    editorState: EditorState;
    updateElementTransform: (id: string, updates: any) => void;
    updateScene: (scene: Scene) => void;
    setSelectedElement: (element: Element | null) => void;
    saveToHistory: (state: EditorState) => void;
    zoomLevel: 'fit' | 25 | 50 | 75 | 100 | 150 | 200;
    setZoomLevel: (zoom: 'fit' | 25 | 50 | 75 | 100 | 150 | 200) => void;
}

export const useKeyboardShortcuts = ({
    copyElement,
    pasteElement,
    duplicateElement,
    selectedElement,
    activeScene,
    editorState,
    updateElementTransform,
    updateScene,
    setSelectedElement,
    saveToHistory,
    zoomLevel,
    setZoomLevel
}: UseKeyboardShortcutsProps) => {
    useEffect(() => {
        const handleKeyDown = (e: KeyboardEvent) => {
            if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
                return;
            }

            if (e.ctrlKey || e.metaKey) {
                switch (e.key.toLowerCase()) {
                    case 'c':
                        e.preventDefault();
                        copyElement();
                        break;
                    case 'v':
                        e.preventDefault();
                        pasteElement();
                        break;
                    case 'd':
                        e.preventDefault();
                        duplicateElement();
                        break;
                    case '=':
                    case '+':
                        e.preventDefault();
                        if (zoomLevel === 'fit') setZoomLevel(100);
                        else if (zoomLevel === 25) setZoomLevel(50);
                        else if (zoomLevel === 50) setZoomLevel(75);
                        else if (zoomLevel === 75) setZoomLevel(100);
                        else if (zoomLevel === 100) setZoomLevel(150);
                        else if (zoomLevel === 150) setZoomLevel(200);
                        break;
                    case '-':
                        e.preventDefault();
                        if (zoomLevel === 200) setZoomLevel(150);
                        else if (zoomLevel === 150) setZoomLevel(100);
                        else if (zoomLevel === 100) setZoomLevel(75);
                        else if (zoomLevel === 75) setZoomLevel(50);
                        else if (zoomLevel === 50) setZoomLevel(25);
                        else if (zoomLevel === 25) setZoomLevel('fit');
                        break;
                    case '0':
                        e.preventDefault();
                        setZoomLevel('fit');
                        break;
                    case '1':
                        e.preventDefault();
                        setZoomLevel(100);
                        break;
                }
            } else {
                switch (e.key) {
                    case 'Delete':
                    case 'Backspace':
                        if (selectedElement && activeScene) {
                            e.preventDefault();
                            saveToHistory(editorState);
                            const updatedScene = {
                                ...activeScene,
                                elements: activeScene.elements.filter(el => el.id !== selectedElement.id)
                            };
                            updateScene(updatedScene);
                            setSelectedElement(null);
                        }
                        break;
                    case 'ArrowUp':
                    case 'ArrowDown':
                    case 'ArrowLeft':
                    case 'ArrowRight':
                        if (selectedElement) {
                            e.preventDefault();
                            const shift = e.shiftKey ? 10 : 1;
                            let deltaX = 0, deltaY = 0;
                            
                            switch (e.key) {
                                case 'ArrowUp': deltaY = -shift; break;
                                case 'ArrowDown': deltaY = shift; break;
                                case 'ArrowLeft': deltaX = -shift; break;
                                case 'ArrowRight': deltaX = shift; break;
                            }
                            
                            updateElementTransform(selectedElement.id, {
                                x: selectedElement.transform.x + deltaX,
                                y: selectedElement.transform.y + deltaY
                            });
                        }
                        break;
                }
            }
        };

        window.addEventListener('keydown', handleKeyDown);
        return () => window.removeEventListener('keydown', handleKeyDown);
    }, [
        copyElement, pasteElement, duplicateElement, 
        selectedElement, activeScene, editorState,
        updateElementTransform, updateScene, setSelectedElement, 
        zoomLevel, setZoomLevel
    ]);
};